/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/login/page";
exports.ids = ["app/login/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'login',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(rsc)/./app/login/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/login/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/login/page\",\n        pathname: \"/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/login/page.tsx */ \"(ssr)/./app/login/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkYW0ncyU1QyU1Q0Rlc2t0b3AlNUMlNUNsYW5kLW1hbmFnZW1lbnQtc3lzdGVtJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNsb2dpbiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBdUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naXMtZnJvbnRlbmQvPzE2NWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZGFtJ3NcXFxcRGVza3RvcFxcXFxsYW5kLW1hbmFnZW1lbnQtc3lzdGVtXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxsb2dpblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Clogin%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/providers.tsx */ \"(ssr)/./app/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkYW0ncyU1QyU1Q0Rlc2t0b3AlNUMlNUNsYW5kLW1hbmFnZW1lbnQtc3lzdGVtJTVDJTVDZnJvbnRlbmQlNUMlNUNhcHAlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0FkYW0ncyU1QyU1Q0Rlc2t0b3AlNUMlNUNsYW5kLW1hbmFnZW1lbnQtc3lzdGVtJTVDJTVDZnJvbnRlbmQlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDQWRhbSdzJTVDJTVDRGVza3RvcCU1QyU1Q2xhbmQtbWFuYWdlbWVudC1zeXN0ZW0lNUMlNUNmcm9udGVuZCU1QyU1Q2FwcCU1QyU1Q2dsb2JhbHMuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrSkFBb0oiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naXMtZnJvbnRlbmQvPzAyNGIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxBZGFtJ3NcXFxcRGVza3RvcFxcXFxsYW5kLW1hbmFnZW1lbnQtc3lzdGVtXFxcXGZyb250ZW5kXFxcXGFwcFxcXFxwcm92aWRlcnMudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CAdam's%5C%5CDesktop%5C%5Cland-management-system%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoginPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/alert */ \"(ssr)/./components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Lock,Mail,Map!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Lock,Mail,Map!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Lock,Mail,Map!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lock.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Lock,Mail,Map!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-2.js\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./lib/api.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\nfunction LoginPage() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { login } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_9__.useAuthStore)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_10__.useForm)();\n    const onSubmit = async (data)=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_8__.authAPI.login(data);\n            const { token, user } = response.data;\n            login(user, token);\n            router.push(\"/dashboard\");\n        } catch (err) {\n            setError(err.response?.data?.error || \"Erreur de connexion\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n            className: \"w-full max-w-md\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-center mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-3 bg-blue-100 rounded-full\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-8 w-8 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                            className: \"text-2xl\",\n                            children: \"Connexion\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                            children: \"Syst\\xe8me de Gestion Fonci\\xe8re et Juridique\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                            onSubmit: handleSubmit(onSubmit),\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"email\",\n                                            children: \"Email\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 66,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"email\",\n                                                    type: \"email\",\n                                                    placeholder: \"<EMAIL>\",\n                                                    className: \"pl-10\",\n                                                    ...register(\"email\", {\n                                                        required: \"Email requis\",\n                                                        pattern: {\n                                                            value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i,\n                                                            message: \"Email invalide\"\n                                                        }\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 67,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: errors.email.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 32\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 63,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                            htmlFor: \"password\",\n                                            children: \"Mot de passe\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"absolute left-3 top-3 h-4 w-4 text-gray-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                    id: \"password\",\n                                                    type: \"password\",\n                                                    placeholder: \"••••••••\",\n                                                    className: \"pl-10\",\n                                                    ...register(\"password\", {\n                                                        required: \"Mot de passe requis\",\n                                                        minLength: {\n                                                            value: 6,\n                                                            message: \"Minimum 6 caract\\xe8res\"\n                                                        }\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.password && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-red-600\",\n                                            children: errors.password.message\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 35\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.Alert, {\n                                    variant: \"destructive\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_7__.AlertDescription, {\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    type: \"submit\",\n                                    className: \"w-full\",\n                                    disabled: loading,\n                                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Lock_Mail_Map_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Connexion...\"\n                                        ]\n                                    }, void 0, true) : \"Se connecter\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 p-4 bg-gray-50 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"Comptes de d\\xe9monstration:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 124,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-1 text-xs text-gray-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"\\uD83D\\uDC51 Admin: <EMAIL> / admin123\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"⚖️ Juriste: <EMAIL> / juriste123\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: \"\\uD83D\\uDC64 Utilisateur: <EMAIL> / user123\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n            lineNumber: 51,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\login\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/login/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-query */ \"(ssr)/./node_modules/react-query/es/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\nfunction Providers({ children }) {\n    const [queryClient] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>new react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n            defaultOptions: {\n                queries: {\n                    staleTime: 5 * 60 * 1000,\n                    cacheTime: 10 * 60 * 1000\n                }\n            }\n        }));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClientProvider, {\n        client: queryClient,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\providers.tsx\",\n        lineNumber: 21,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvcHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBSThEO0FBQzlCO0FBRXpCLFNBQVNHLFVBQVUsRUFBRUMsUUFBUSxFQUFpQztJQUNuRSxNQUFNLENBQUNDLFlBQVksR0FBR0gsK0NBQVFBLENBQzVCLElBQ0UsSUFBSUYsb0RBQVdBLENBQUM7WUFDZE0sZ0JBQWdCO2dCQUNkQyxTQUFTO29CQUNQQyxXQUFXLElBQUksS0FBSztvQkFDcEJDLFdBQVcsS0FBSyxLQUFLO2dCQUN2QjtZQUNGO1FBQ0Y7SUFHSixxQkFBTyw4REFBQ1IsNERBQW1CQTtRQUFDUyxRQUFRTDtrQkFBY0Q7Ozs7OztBQUNwRCIsInNvdXJjZXMiOlsid2VicGFjazovL2dpcy1mcm9udGVuZC8uL2FwcC9wcm92aWRlcnMudHN4P2NlNDYiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgUXVlcnlDbGllbnQsIFF1ZXJ5Q2xpZW50UHJvdmlkZXIgfSBmcm9tIFwicmVhY3QtcXVlcnlcIlxuaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tIFwicmVhY3RcIlxuXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcbiAgY29uc3QgW3F1ZXJ5Q2xpZW50XSA9IHVzZVN0YXRlKFxuICAgICgpID0+XG4gICAgICBuZXcgUXVlcnlDbGllbnQoe1xuICAgICAgICBkZWZhdWx0T3B0aW9uczoge1xuICAgICAgICAgIHF1ZXJpZXM6IHtcbiAgICAgICAgICAgIHN0YWxlVGltZTogNSAqIDYwICogMTAwMCwgLy8gNSBtaW51dGVzXG4gICAgICAgICAgICBjYWNoZVRpbWU6IDEwICogNjAgKiAxMDAwLCAvLyAxMCBtaW51dGVzXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0pLFxuICApXG5cbiAgcmV0dXJuIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PntjaGlsZHJlbn08L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwidXNlU3RhdGUiLCJQcm92aWRlcnMiLCJjaGlsZHJlbiIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwiY2FjaGVUaW1lIiwiY2xpZW50Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/providers.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/alert.tsx":
/*!*********************************!*\
  !*** ./components/ui/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n// components/ui/alert.tsx\n\n\nfunction Alert({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border p-4 bg-yellow-100 text-yellow-800\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 5,\n        columnNumber: 10\n    }, this);\n}\nfunction AlertDescription({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        className: \"text-sm mt-1\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 9,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2FsZXJ0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQSwwQkFBMEI7O0FBQ0Q7QUFFbEIsU0FBU0MsTUFBTSxFQUFFQyxRQUFRLEVBQWlDO0lBQy9ELHFCQUFPLDhEQUFDQztRQUFJQyxXQUFVO2tCQUE0Q0Y7Ozs7OztBQUNwRTtBQUVPLFNBQVNHLGlCQUFpQixFQUFFSCxRQUFRLEVBQWlDO0lBQzFFLHFCQUFPLDhEQUFDSTtRQUFFRixXQUFVO2tCQUFnQkY7Ozs7OztBQUN0QyIsInNvdXJjZXMiOlsid2VicGFjazovL2dpcy1mcm9udGVuZC8uL2NvbXBvbmVudHMvdWkvYWxlcnQudHN4P2E2YzEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gY29tcG9uZW50cy91aS9hbGVydC50c3hcclxuaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQWxlcnQoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xyXG4gIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cImJvcmRlciBwLTQgYmcteWVsbG93LTEwMCB0ZXh0LXllbGxvdy04MDBcIj57Y2hpbGRyZW59PC9kaXY+XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBBbGVydERlc2NyaXB0aW9uKHsgY2hpbGRyZW4gfTogeyBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlIH0pIHtcclxuICByZXR1cm4gPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBtdC0xXCI+e2NoaWxkcmVufTwvcD5cclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJBbGVydCIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwiQWxlcnREZXNjcmlwdGlvbiIsInAiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Button({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        ...props,\n        className: \"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2J1dHRvbi50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTBCO0FBRW5CLFNBQVNDLE9BQU8sRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQXNEO0lBQzFGLHFCQUFPLDhEQUFDQztRQUFRLEdBQUdELEtBQUs7UUFBRUUsV0FBVTtrQkFBbUZIOzs7Ozs7QUFDekgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naXMtZnJvbnRlbmQvLi9jb21wb25lbnRzL3VpL2J1dHRvbi50c3g/ODk0NCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gQnV0dG9uKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFJlYWN0LkJ1dHRvbkhUTUxBdHRyaWJ1dGVzPEhUTUxCdXR0b25FbGVtZW50Pikge1xyXG4gIHJldHVybiA8YnV0dG9uIHsuLi5wcm9wc30gY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWJsdWUtNjAwIHRleHQtd2hpdGUgcm91bmRlZCBob3ZlcjpiZy1ibHVlLTcwMCBkaXNhYmxlZDpvcGFjaXR5LTUwXCIgPntjaGlsZHJlbn08L2J1dHRvbj47XHJcbn0gIl0sIm5hbWVzIjpbIlJlYWN0IiwiQnV0dG9uIiwiY2hpbGRyZW4iLCJwcm9wcyIsImJ1dHRvbiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Card({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ...props,\n        className: \"bg-white rounded shadow p-4 mb-4\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\nfunction CardHeader({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ...props,\n        className: \"mb-2 font-bold\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\nfunction CardTitle({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n        ...props,\n        className: \"text-lg font-semibold\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\nfunction CardDescription({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ...props,\n        className: \"text-gray-500 text-sm\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 16,\n        columnNumber: 10\n    }, this);\n}\nfunction CardContent({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 20,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().forwardRef((props, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        ref: ref,\n        ...props,\n        className: \"border rounded px-3 py-2 w-full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 4,\n        columnNumber: 19\n    }, undefined));\nInput.displayName = \"Input\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFbkIsTUFBTUMsc0JBQVFELHVEQUFnQixDQUNuQyxDQUFDRyxPQUFPQyxvQkFBUSw4REFBQ0M7UUFBTUQsS0FBS0E7UUFBTSxHQUFHRCxLQUFLO1FBQUVHLFdBQVU7Ozs7O21CQUN0RDtBQUNGTCxNQUFNTSxXQUFXLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naXMtZnJvbnRlbmQvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeD9kYTc5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuSW5wdXRIVE1MQXR0cmlidXRlczxIVE1MSW5wdXRFbGVtZW50Pj4oXHJcbiAgKHByb3BzLCByZWYpID0+IDxpbnB1dCByZWY9e3JlZn0gey4uLnByb3BzfSBjbGFzc05hbWU9XCJib3JkZXIgcm91bmRlZCBweC0zIHB5LTIgdy1mdWxsXCIgLz5cclxuKTtcclxuSW5wdXQuZGlzcGxheU5hbWUgPSBcIklucHV0XCI7ICJdLCJuYW1lcyI6WyJSZWFjdCIsIklucHV0IiwiZm9yd2FyZFJlZiIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJjbGFzc05hbWUiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Label({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n        ...props,\n        className: \"block mb-1 font-medium text-gray-700\",\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMEI7QUFFbkIsU0FBU0MsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsT0FBb0Q7SUFDdkYscUJBQU8sOERBQUNDO1FBQU8sR0FBR0QsS0FBSztRQUFFRSxXQUFVO2tCQUF3Q0g7Ozs7OztBQUM3RSIsInNvdXJjZXMiOlsid2VicGFjazovL2dpcy1mcm9udGVuZC8uL2NvbXBvbmVudHMvdWkvbGFiZWwudHN4Pzg4ZWQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIExhYmVsKHsgY2hpbGRyZW4sIC4uLnByb3BzIH06IFJlYWN0LkxhYmVsSFRNTEF0dHJpYnV0ZXM8SFRNTExhYmVsRWxlbWVudD4pIHtcclxuICByZXR1cm4gPGxhYmVsIHsuLi5wcm9wc30gY2xhc3NOYW1lPVwiYmxvY2sgbWItMSBmb250LW1lZGl1bSB0ZXh0LWdyYXktNzAwXCI+e2NoaWxkcmVufTwvbGFiZWw+O1xyXG59ICJdLCJuYW1lcyI6WyJSZWFjdCIsIkxhYmVsIiwiY2hpbGRyZW4iLCJwcm9wcyIsImxhYmVsIiwiY2xhc3NOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api.ts":
/*!********************!*\
  !*** ./lib/api.ts ***!
  \********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authAPI: () => (/* binding */ authAPI),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   documentsAPI: () => (/* binding */ documentsAPI),\n/* harmony export */   parcellesAPI: () => (/* binding */ parcellesAPI),\n/* harmony export */   proprietairesAPI: () => (/* binding */ proprietairesAPI),\n/* harmony export */   usersAPI: () => (/* binding */ usersAPI)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst API_BASE_URL = \"http://localhost:5000/api\" || 0;\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].create({\n    baseURL: API_BASE_URL,\n    timeout: 10000,\n    headers: {\n        \"Content-Type\": \"application/json\"\n    }\n});\n// Request interceptor to add auth token\napi.interceptors.request.use((config)=>{\n    const token = localStorage.getItem(\"token\");\n    if (token) {\n        config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor to handle errors\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401) {\n        localStorage.removeItem(\"token\");\n        localStorage.removeItem(\"user\");\n        window.location.href = \"/login\";\n    }\n    return Promise.reject(error);\n});\n// Auth API\nconst authAPI = {\n    login: (credentials)=>api.post(\"/auth/login\", credentials),\n    register: (userData)=>api.post(\"/auth/register\", userData),\n    logout: ()=>api.post(\"/auth/logout\"),\n    changePassword: (data)=>api.post(\"/auth/change-password\", data)\n};\n// Parcelles API\nconst parcellesAPI = {\n    getAll: (params)=>api.get(\"/parcelles\", {\n            params\n        }),\n    getById: (id)=>api.get(`/parcelles/${id}`),\n    create: (data)=>api.post(\"/parcelles\", data),\n    update: (id, data)=>api.put(`/parcelles/${id}`, data),\n    delete: (id)=>api.delete(`/parcelles/${id}`),\n    getInBounds: async (bounds)=>{\n        // Dummy implementation\n        return {\n            data: []\n        };\n    },\n    getStatistics: ()=>api.get(\"/parcelles/statistics\")\n};\n// Documents API\nconst documentsAPI = {\n    getAll: (params)=>api.get(\"/documents\", {\n            params\n        }),\n    getById: (id)=>api.get(`/documents/${id}`),\n    upload: (formData)=>api.post(\"/documents/upload\", formData, {\n            headers: {\n                \"Content-Type\": \"multipart/form-data\"\n            }\n        }),\n    update: (id, data)=>api.put(`/documents/${id}`, data),\n    delete: (id)=>api.delete(`/documents/${id}`),\n    download: (id)=>api.get(`/documents/${id}/download`, {\n            responseType: \"blob\"\n        }),\n    getStatistics: ()=>api.get(\"/documents/statistics\")\n};\n// Proprietaires API\nconst proprietairesAPI = {\n    getAll: (params)=>api.get(\"/proprietaires\", {\n            params\n        }),\n    getById: (id)=>api.get(`/proprietaires/${id}`),\n    create: (data)=>api.post(\"/proprietaires\", data),\n    update: (id, data)=>api.put(`/proprietaires/${id}`, data),\n    delete: (id)=>api.delete(`/proprietaires/${id}`)\n};\n// Users API\nconst usersAPI = {\n    getAll: (params)=>api.get(\"/users\", {\n            params\n        }),\n    getById: (id)=>api.get(`/users/${id}`),\n    update: (id, data)=>api.put(`/users/${id}`, data),\n    delete: (id)=>api.delete(`/users/${id}`)\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api.ts\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAppStore: () => (/* binding */ useAppStore),\n/* harmony export */   useAuthStore: () => (/* binding */ useAuthStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/index.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useAuthStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        user: null,\n        token: null,\n        isAuthenticated: false,\n        login: (user, token)=>{\n            localStorage.setItem(\"token\", token);\n            localStorage.setItem(\"user\", JSON.stringify(user));\n            set({\n                user,\n                token,\n                isAuthenticated: true\n            });\n        },\n        logout: ()=>{\n            localStorage.removeItem(\"token\");\n            localStorage.removeItem(\"user\");\n            set({\n                user: null,\n                token: null,\n                isAuthenticated: false\n            });\n        },\n        updateUser: (userData)=>{\n            const currentUser = get().user;\n            if (currentUser) {\n                const updatedUser = {\n                    ...currentUser,\n                    ...userData\n                };\n                localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n                set({\n                    user: updatedUser\n                });\n            }\n        }\n    }), {\n    name: \"auth-storage\",\n    partialize: (state)=>({\n            user: state.user,\n            token: state.token,\n            isAuthenticated: state.isAuthenticated\n        })\n}));\nconst useAppStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)((set, get)=>({\n        sidebarOpen: true,\n        activeTab: \"dashboard\",\n        notifications: [],\n        setSidebarOpen: (open)=>set({\n                sidebarOpen: open\n            }),\n        setActiveTab: (tab)=>set({\n                activeTab: tab\n            }),\n        addNotification: (notification)=>{\n            const notifications = get().notifications;\n            set({\n                notifications: [\n                    notification,\n                    ...notifications\n                ]\n            });\n        },\n        removeNotification: (id)=>{\n            const notifications = get().notifications.filter((n)=>n.id !== id);\n            set({\n                notifications\n            });\n        },\n        markNotificationAsRead: (id)=>{\n            const notifications = get().notifications.map((n)=>n.id === id ? {\n                    ...n,\n                    isRead: true\n                } : n);\n            set({\n                notifications\n            });\n        }\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0f59f5f1ec15\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9naXMtZnJvbnRlbmQvLi9hcHAvZ2xvYmFscy5jc3M/MWM4MiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjBmNTlmNWYxZWMxNVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./providers */ \"(rsc)/./app/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"SIGWEB - Syst\\xe8me de Gestion Fonci\\xe8re\",\n    description: \"Syst\\xe8me de Gestion Fonci\\xe8re et Juridique avec support GIS et OCR\",\n    keywords: [\n        \"GIS\",\n        \"Land Management\",\n        \"Contracts\",\n        \"OCR\",\n        \"PostGIS\"\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"fr\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\land-management-system\\\\frontend\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFNTUE7QUFIZ0I7QUFDaUI7QUFJaEMsTUFBTUUsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO1FBQUM7UUFBTztRQUFtQjtRQUFhO1FBQU87S0FBVTtBQUNyRSxFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO1lBQUtDLFdBQVdYLDJKQUFlO3NCQUM5Qiw0RUFBQ0MsaURBQVNBOzBCQUFFTTs7Ozs7Ozs7Ozs7Ozs7OztBQUlwQiIsInNvdXJjZXMiOlsid2VicGFjazovL2dpcy1mcm9udGVuZC8uL2FwcC9sYXlvdXQudHN4Pzk5ODgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5pbXBvcnQgeyBJbnRlciB9IGZyb20gXCJuZXh0L2ZvbnQvZ29vZ2xlXCJcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIlxuaW1wb3J0IHsgUHJvdmlkZXJzIH0gZnJvbSBcIi4vcHJvdmlkZXJzXCJcblxuY29uc3QgaW50ZXIgPSBJbnRlcih7IHN1YnNldHM6IFtcImxhdGluXCJdIH0pXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiBcIlNJR1dFQiAtIFN5c3TDqG1lIGRlIEdlc3Rpb24gRm9uY2nDqHJlXCIsXG4gIGRlc2NyaXB0aW9uOiBcIlN5c3TDqG1lIGRlIEdlc3Rpb24gRm9uY2nDqHJlIGV0IEp1cmlkaXF1ZSBhdmVjIHN1cHBvcnQgR0lTIGV0IE9DUlwiLFxuICBrZXl3b3JkczogW1wiR0lTXCIsIFwiTGFuZCBNYW5hZ2VtZW50XCIsIFwiQ29udHJhY3RzXCIsIFwiT0NSXCIsIFwiUG9zdEdJU1wiXSxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImZyXCI+XG4gICAgICA8Ym9keSBjbGFzc05hbWU9e2ludGVyLmNsYXNzTmFtZX0+XG4gICAgICAgIDxQcm92aWRlcnM+e2NoaWxkcmVufTwvUHJvdmlkZXJzPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbImludGVyIiwiUHJvdmlkZXJzIiwibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwia2V5d29yZHMiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/login/page.tsx":
/*!****************************!*\
  !*** ./app/login/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\land-management-system\frontend\app\login\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/providers.tsx":
/*!***************************!*\
  !*** ./app/providers.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\land-management-system\frontend\app\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/react-query","vendor-chunks/zustand","vendor-chunks/use-sync-external-store","vendor-chunks/@swc","vendor-chunks/@babel","vendor-chunks/axios","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/lucide-react","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/react-hook-form","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flogin%2Fpage&page=%2Flogin%2Fpage&appPaths=%2Flogin%2Fpage&pagePath=private-next-app-dir%2Flogin%2Fpage.tsx&appDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CAdam's%5CDesktop%5Cland-management-system%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();