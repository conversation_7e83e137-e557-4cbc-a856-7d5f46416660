const express = require("express")
const proprietaireController = require("../controllers/proprietaireController")
const { authenticateToken, requireRole } = require("../middleware/auth")
const { validateProprietaire } = require("../middleware/validation")

const router = express.Router()

// All routes require authentication
router.use(authenticateToken)

/**
 * @swagger
 * /api/proprietaires:
 *   get:
 *     summary: Get all proprietaires
 *     tags: [Proprietaires]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search term
 *     responses:
 *       200:
 *         description: List of proprietaires
 */
router.get("/", proprietaireController.getAllProprietaires)

/**
 * @swagger
 * /api/proprietaires/statistics:
 *   get:
 *     summary: Get proprietaires statistics
 *     tags: [Pro<PERSON>rietaires]
 *     security:
 *       - bearerAuth: []
 */
router.get("/statistics", proprietaireController.getProprietaireStatistics)

/**
 * @swagger
 * /api/proprietaires/{id}:
 *   get:
 *     summary: Get proprietaire by ID
 *     tags: [Proprietaires]
 *     security:
 *       - bearerAuth: []
 */
router.get("/:id", proprietaireController.getProprietaireById)

/**
 * @swagger
 * /api/proprietaires:
 *   post:
 *     summary: Create new proprietaire
 *     tags: [Proprietaires]
 *     security:
 *       - bearerAuth: []
 */
router.post("/", requireRole(["ADMIN", "JURISTE"]), validateProprietaire, proprietaireController.createProprietaire)

/**
 * @swagger
 * /api/proprietaires/{id}:
 *   put:
 *     summary: Update proprietaire
 *     tags: [Proprietaires]
 *     security:
 *       - bearerAuth: []
 */
router.put("/:id", requireRole(["ADMIN", "JURISTE"]), proprietaireController.updateProprietaire)

/**
 * @swagger
 * /api/proprietaires/{id}:
 *   delete:
 *     summary: Delete proprietaire
 *     tags: [Proprietaires]
 *     security:
 *       - bearerAuth: []
 */
router.delete("/:id", requireRole(["ADMIN"]), proprietaireController.deleteProprietaire)

module.exports = router
