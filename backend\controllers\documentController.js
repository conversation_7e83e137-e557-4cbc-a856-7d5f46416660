const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, User } = require("../models")
const { Op, sequelize } = require("sequelize")
const { logger, logAuditEvent } = require("../utils/logger")
const Tesseract = require("tesseract.js")
const path = require("path")
const fs = require("fs")

class DocumentController {
  async uploadDocument(req, res) {
    try {
      const { parcelleId, proprietaireId, type, clauses, montant, devise = "MAD" } = req.body
      const file = req.file

      if (!file) {
        return res.status(400).json({
          error: "Aucun fichier téléchargé",
        })
      }

      if (!parcelleId || !type) {
        return res.status(400).json({
          error: "Parcelle et type de document requis",
        })
      }

      // Verify parcelle exists
      const parcelle = await Parcelle.findByPk(parcelleId)
      if (!parcelle) {
        return res.status(404).json({
          error: "<PERSON><PERSON><PERSON> non trouvée",
        })
      }

      // Verify proprietaire exists if provided
      if (proprietaireId) {
        const proprietaire = await Proprietaire.findByPk(proprietaireId)
        if (!proprietaire) {
          return res.status(404).json({
            error: "Propriétaire non trouvé",
          })
        }
      }

      // Generate unique document number
      const documentNumber = `DOC-${Date.now()}-${Math.random().toString(36).substr(2, 5)}`

      // Create document record using your exact field names
      const document = await Document.create({
        numero: documentNumber,
        nom: file.originalname,
        type,
        fichierPath: file.path,
        fichierOriginalName: file.originalname,
        fichierSize: file.size,
        fichierMimeType: file.mimetype,
        clauses,
        montant: montant ? Number.parseFloat(montant) : null,
        devise,
        ParcelleIdParcelle: parcelleId, // Using your exact foreign key name
        ProprietaireId: proprietaireId, // Using your exact foreign key name
        UtilisateurId: req.user.id, // Using your exact foreign key name
        dateSignature: new Date(),
      })

      await logAuditEvent("DOCUMENT_UPLOADED", req.user.id, {
        documentId: document.id,
        fileName: file.originalname,
        fileSize: file.size,
        parcelleId,
        type,
      })

      logger.info("Document uploaded", {
        documentId: document.id,
        fileName: file.originalname,
        uploadedBy: req.user.id,
      })

      // Process OCR for images
      if (file.mimetype.startsWith("image/")) {
        this.processOCR(document, file.path, req.user.id)
      }

      res.status(201).json({
        message: "Document téléchargé avec succès",
        document: {
          id: document.id,
          numero: document.numero,
          nom: document.nom,
          type: document.type,
          statut: document.statut,
          isOcrProcessed: document.isOcrProcessed,
        },
      })
    } catch (error) {
      logger.error("Document upload error:", error)
      res.status(500).json({
        error: "Erreur lors du téléchargement du document",
      })
    }
  }

  async processOCR(document, filePath, userId) {
    try {
      logger.info("Starting OCR processing", {
        documentId: document.id,
        filePath,
      })

      const startTime = Date.now()

      const {
        data: { text, confidence },
      } = await Tesseract.recognize(filePath, "ara+fra", {
        logger: (m) => logger.debug("Tesseract progress:", m),
      })

      const processingTime = Date.now() - startTime

      // Save OCR text to file
      const txtFilePath = filePath.replace(path.extname(filePath), ".txt")
      fs.writeFileSync(txtFilePath, text, "utf8")

      // Save OCR result to database
      await OCRText.create({
        documentId: document.id,
        extractedText: text,
        confidence: confidence,
        language: "ara+fra",
        processingTime,
        textFilePath: txtFilePath,
      })

      // Update document status
      await document.update({ isOcrProcessed: true })

      await logAuditEvent("OCR_PROCESSED", userId, {
        documentId: document.id,
        confidence,
        textLength: text.length,
        processingTime,
      })

      logger.info("OCR processing completed", {
        documentId: document.id,
        confidence,
        processingTime,
      })
    } catch (error) {
      logger.error("OCR processing failed:", error)

      await logAuditEvent("OCR_FAILED", userId, {
        documentId: document.id,
        error: error.message,
      })
    }
  }

  async getAllDocuments(req, res) {
    try {
      const { page = 1, limit = 10, search, type, statut, parcelleId, proprietaireId } = req.query

      const offset = (page - 1) * limit
      const whereClause = {}

      if (search) {
        whereClause[Op.or] = [{ numero: { [Op.iLike]: `%${search}%` } }, { nom: { [Op.iLike]: `%${search}%` } }]
      }

      if (type) whereClause.type = type
      if (statut) whereClause.statut = statut
      if (parcelleId) whereClause.ParcelleIdParcelle = parcelleId
      if (proprietaireId) whereClause.ProprietaireId = proprietaireId

      const { count, rows: documents } = await Document.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Parcelle,
            as: "parcelle",
            attributes: ["idParcelle", "numeroTitreFoncier", "adresse"],
          },
          {
            model: Proprietaire,
            as: "proprietaire",
            attributes: ["idProprietaire", "nom", "prenom", "cin"],
          },
          {
            model: User,
            as: "uploader",
            attributes: ["id", "nom", "email"],
          },
          {
            model: OCRText,
            as: "ocrText",
          },
        ],
        limit: Number.parseInt(limit),
        offset: Number.parseInt(offset),
        order: [["createdAt", "DESC"]],
      })

      res.json({
        documents,
        pagination: {
          currentPage: Number.parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: Number.parseInt(limit),
        },
      })
    } catch (error) {
      logger.error("Get documents error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des documents",
      })
    }
  }

  async getDocumentById(req, res) {
    try {
      const { id } = req.params

      const document = await Document.findByPk(id, {
        include: [
          {
            model: Parcelle,
            as: "parcelle",
          },
          {
            model: Proprietaire,
            as: "proprietaire",
          },
          {
            model: User,
            as: "uploader",
            attributes: ["id", "nom", "email"],
          },
          {
            model: OCRText,
            as: "ocrText",
          },
        ],
      })

      if (!document) {
        return res.status(404).json({
          error: "Document non trouvé",
        })
      }

      res.json(document)
    } catch (error) {
      logger.error("Get document by ID error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération du document",
      })
    }
  }

  async updateDocument(req, res) {
    try {
      const { id } = req.params
      const updateData = req.body

      const document = await Document.findByPk(id)
      if (!document) {
        return res.status(404).json({
          error: "Document non trouvé",
        })
      }

      const oldValues = document.toJSON()
      await document.update(updateData)

      await logAuditEvent("DOCUMENT_UPDATED", req.user.id, {
        documentId: document.id,
        oldValues,
        newValues: updateData,
      })

      logger.info("Document updated", {
        documentId: document.id,
        updatedBy: req.user.id,
      })

      res.json({
        message: "Document mis à jour avec succès",
        document,
      })
    } catch (error) {
      logger.error("Update document error:", error)
      res.status(500).json({
        error: "Erreur lors de la mise à jour du document",
      })
    }
  }

  async deleteDocument(req, res) {
    try {
      const { id } = req.params

      const document = await Document.findByPk(id, {
        include: [{ model: OCRText, as: "ocrText" }],
      })

      if (!document) {
        return res.status(404).json({
          error: "Document non trouvé",
        })
      }

      // Delete physical files
      if (document.fichierPath && fs.existsSync(document.fichierPath)) {
        fs.unlinkSync(document.fichierPath)
      }

      if (document.ocrText && document.ocrText.textFilePath && fs.existsSync(document.ocrText.textFilePath)) {
        fs.unlinkSync(document.ocrText.textFilePath)
      }

      await document.destroy()

      await logAuditEvent("DOCUMENT_DELETED", req.user.id, {
        documentId: document.id,
        fileName: document.nom,
      })

      logger.info("Document deleted", {
        documentId: document.id,
        deletedBy: req.user.id,
      })

      res.json({
        message: "Document supprimé avec succès",
      })
    } catch (error) {
      logger.error("Delete document error:", error)
      res.status(500).json({
        error: "Erreur lors de la suppression du document",
      })
    }
  }

  async downloadDocument(req, res) {
    try {
      const { id } = req.params

      const document = await Document.findByPk(id)
      if (!document) {
        return res.status(404).json({
          error: "Document non trouvé",
        })
      }

      if (!document.fichierPath || !fs.existsSync(document.fichierPath)) {
        return res.status(404).json({
          error: "Fichier non trouvé sur le serveur",
        })
      }

      await logAuditEvent("DOCUMENT_DOWNLOADED", req.user.id, {
        documentId: document.id,
        fileName: document.nom,
      })

      res.download(document.fichierPath, document.fichierOriginalName)
    } catch (error) {
      logger.error("Download document error:", error)
      res.status(500).json({
        error: "Erreur lors du téléchargement du document",
      })
    }
  }

  async getDocumentStatistics(req, res) {
    try {
      const stats = await Document.findAll({
        attributes: ["type", "statut", [sequelize.fn("COUNT", sequelize.col("id")), "count"]],
        group: ["type", "statut"],
      })

      const totalDocuments = await Document.count()
      const ocrProcessed = await Document.count({ where: { isOcrProcessed: true } })
      const ocrSuccessRate = totalDocuments > 0 ? ((ocrProcessed / totalDocuments) * 100).toFixed(2) : 0

      res.json({
        totalDocuments,
        ocrProcessed,
        ocrSuccessRate: Number.parseFloat(ocrSuccessRate),
        statistiquesParType: stats,
      })
    } catch (error) {
      logger.error("Get document statistics error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des statistiques",
      })
    }
  }
}

module.exports = new DocumentController()
