const { sequelize } = require("../models")
const bcrypt = require("bcryptjs")

async function initializeDatabase() {
  try {
    // Sync all models with the database
    await sequelize.sync({ alter: true })
    console.log("Database synchronized successfully")

    // Create default admin user if it doesn't exist
    const User = require("../models").User
    const adminExists = await User.findOne({
      where: { email: "<EMAIL>" },
    })

    if (!adminExists) {
      const hashedPassword = await bcrypt.hash("admin123", 10)
      await User.create({
        username: "admin",
        email: "<EMAIL>",
        password: hashedPassword,
        nom: "Admin",
        prenom: "System",
        role: "admin",
        isActive: true,
      })
      console.log("Default admin user created")
    }

    console.log("Database initialization completed")
  } catch (error) {
    console.error("Error initializing database:", error)
    process.exit(1)
  }
}

// Run the initialization
initializeDatabase() 