{"level":"info","message":"Database connection established successfully","service":"gis-land-management","timestamp":"2025-06-10 15:41:39"}
{"level":"error","message":"Unable to start server: default for column \"role\" cannot be cast automatically to type enum_users_role","name":"SequelizeDatabaseError","original":{"code":"42804","file":"tablecmds.c","length":149,"line":"13205","name":"error","routine":"ATExecAlterColumnType","severity":"ERROR","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");"},"parameters":{},"parent":{"code":"42804","file":"tablecmds.c","length":149,"line":"13205","name":"error","routine":"ATExecAlterColumnType","severity":"ERROR","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");"},"service":"gis-land-management","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\server.js:179:5)","timestamp":"2025-06-10 15:41:39"}
{"level":"info","message":"Database connection established successfully","service":"gis-land-management","timestamp":"2025-06-10 15:45:54"}
{"level":"error","message":"Unable to start server: column \"mot_de_passe\" of relation \"users\" contains null values","name":"SequelizeDatabaseError","original":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"parameters":{},"parent":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"service":"gis-land-management","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.addColumn (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:184:12)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\model.js:958:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\server.js:179:5)","timestamp":"2025-06-10 15:45:54"}
{"level":"info","message":"Database connection established successfully","service":"gis-land-management","timestamp":"2025-06-10 15:47:49"}
{"level":"error","message":"Unable to start server: column \"mot_de_passe\" of relation \"users\" contains null values","name":"SequelizeDatabaseError","original":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"parameters":{},"parent":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"service":"gis-land-management","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.addColumn (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:184:12)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\model.js:958:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\server.js:179:5)","timestamp":"2025-06-10 15:47:49"}
{"level":"info","message":"Database connection established successfully","service":"gis-land-management","timestamp":"2025-06-10 15:49:18"}
{"level":"error","message":"Unable to start server: default for column \"role\" cannot be cast automatically to type enum_users_role","name":"SequelizeDatabaseError","original":{"code":"42804","file":"tablecmds.c","length":149,"line":"13205","name":"error","routine":"ATExecAlterColumnType","severity":"ERROR","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");"},"parameters":{},"parent":{"code":"42804","file":"tablecmds.c","length":149,"line":"13205","name":"error","routine":"ATExecAlterColumnType","severity":"ERROR","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");"},"service":"gis-land-management","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\server.js:179:5)","timestamp":"2025-06-10 15:49:18"}
{"level":"info","message":"Database connection established successfully","service":"gis-land-management","timestamp":"2025-06-10 15:56:17"}
{"level":"error","message":"Unable to start server: column \"mot_de_passe\" of relation \"users\" contains null values","name":"SequelizeDatabaseError","original":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"parameters":{},"parent":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"service":"gis-land-management","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.addColumn (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:184:12)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\model.js:958:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\server.js:179:5)","timestamp":"2025-06-10 15:56:17"}
{"level":"info","message":"Database connection established successfully","service":"gis-land-management","timestamp":"2025-06-10 17:22:03"}
{"level":"error","message":"Unable to start server: default for column \"role\" cannot be cast automatically to type enum_users_role","name":"SequelizeDatabaseError","original":{"code":"42804","file":"tablecmds.c","length":149,"line":"13205","name":"error","routine":"ATExecAlterColumnType","severity":"ERROR","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");"},"parameters":{},"parent":{"code":"42804","file":"tablecmds.c","length":149,"line":"13205","name":"error","routine":"ATExecAlterColumnType","severity":"ERROR","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");"},"service":"gis-land-management","sql":"ALTER TABLE \"users\" ALTER COLUMN \"role\" DROP NOT NULL;ALTER TABLE \"users\" ALTER COLUMN \"role\" SET DEFAULT 'STANDARD';DO 'BEGIN CREATE TYPE \"public\".\"enum_users_role\" AS ENUM(''ADMIN'', ''JURIDIQUE'', ''STANDARD''); EXCEPTION WHEN duplicate_object THEN null; END';ALTER TABLE \"users\" ALTER COLUMN \"role\" TYPE \"public\".\"enum_users_role\" USING (\"role\"::\"public\".\"enum_users_role\");","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\model.js:984:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\server.js:179:5)","timestamp":"2025-06-10 17:22:03"}
{"level":"info","message":"Database connection established successfully","service":"gis-land-management","timestamp":"2025-06-10 17:29:19"}
{"level":"error","message":"Unable to start server: column \"mot_de_passe\" of relation \"users\" contains null values","name":"SequelizeDatabaseError","original":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"parameters":{},"parent":{"code":"23502","column":"mot_de_passe","file":"tablecmds.c","length":154,"line":"6284","name":"error","routine":"ATRewriteTable","schema":"public","severity":"ERROR","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","table":"users"},"service":"gis-land-management","sql":"ALTER TABLE \"public\".\"users\" ADD COLUMN \"mot_de_passe\" VARCHAR(255) NOT NULL;","stack":"Error\n    at Query.run (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\postgres\\query.js:50:25)\n    at C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:315:28\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async PostgresQueryInterface.addColumn (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\query-interface.js:184:12)\n    at async User.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\model.js:958:11)\n    at async Sequelize.sync (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\node_modules\\sequelize\\lib\\sequelize.js:377:9)\n    at async startServer (C:\\Users\\<USER>\\Desktop\\land-management-system\\backend\\server.js:179:5)","timestamp":"2025-06-10 17:29:19"}
