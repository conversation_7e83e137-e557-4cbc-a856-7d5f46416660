"use client"

import { useState, useEffect } from "react"
import { LoginForm } from "@/components/login-form"
import { Dashboard } from "@/components/dashboard"
import { MapViewer } from "@/components/map-viewer"
import { DocumentUpload } from "@/components/document-upload"
import { UserManagement } from "@/components/user-management"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { LogOut, Map, Upload, Users, FileText, TrendingUp, Download } from "lucide-react"
// Import the new ContractsManagement component
import { ContractsManagement } from "@/components/contracts-management"
// Import the RealTimeNotifications component
import { RealTimeNotifications } from "@/components/real-time-notifications"
// Add the new components to the imports
import { AnalyticsDashboard } from "@/components/analytics-dashboard"
import { ExportReports } from "@/components/export-reports"

interface User {
  id: string
  email: string
  nom: string
  role: "ADMIN" | "JURIDIQUE" | "STANDARD"
}

export default function App() {
  const [user, setUser] = useState<User | null>(null)
  const [activeTab, setActiveTab] = useState("dashboard")

  useEffect(() => {
    // Check for existing session
    const savedUser = localStorage.getItem("user")
    if (savedUser) {
      setUser(JSON.parse(savedUser))
    }
  }, [])

  const handleLogin = (userData: User) => {
    setUser(userData)
    localStorage.setItem("user", JSON.stringify(userData))
  }

  const handleLogout = () => {
    setUser(null)
    localStorage.removeItem("user")
    setActiveTab("dashboard")
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4">
        <LoginForm onLogin={handleLogin} />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <Map className="h-8 w-8 text-blue-600" />
              <h1 className="text-xl font-semibold text-gray-900">Système de Gestion Foncière</h1>
            </div>
            {/* Add the notifications component to the header section */}
            <div className="flex items-center space-x-4">
              <RealTimeNotifications />
              <span className="text-sm text-gray-600">
                {user.nom} ({user.role})
              </span>
              <Button variant="outline" size="sm" onClick={handleLogout}>
                <LogOut className="h-4 w-4 mr-2" />
                Déconnexion
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-7">
            <TabsTrigger value="dashboard">
              <FileText className="h-4 w-4 mr-2" />
              Tableau de Bord
            </TabsTrigger>
            <TabsTrigger value="map">
              <Map className="h-4 w-4 mr-2" />
              Carte Interactive
            </TabsTrigger>
            {(user.role === "ADMIN" || user.role === "JURIDIQUE") && (
              <TabsTrigger value="upload">
                <Upload className="h-4 w-4 mr-2" />
                Upload Documents
              </TabsTrigger>
            )}
            {user.role === "ADMIN" && (
              <TabsTrigger value="users">
                <Users className="h-4 w-4 mr-2" />
                Gestion Utilisateurs
              </TabsTrigger>
            )}
            <TabsTrigger value="contracts">
              <FileText className="h-4 w-4 mr-2" />
              Contrats
            </TabsTrigger>
            {user.role === "ADMIN" && (
              <TabsTrigger value="analytics">
                <TrendingUp className="h-4 w-4 mr-2" />
                Analytics
              </TabsTrigger>
            )}
            <TabsTrigger value="reports">
              <Download className="h-4 w-4 mr-2" />
              Rapports
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="mt-6">
            <Dashboard user={user} />
          </TabsContent>

          <TabsContent value="map" className="mt-6">
            <MapViewer user={user} />
          </TabsContent>

          {(user.role === "ADMIN" || user.role === "JURIDIQUE") && (
            <TabsContent value="upload" className="mt-6">
              <DocumentUpload user={user} />
            </TabsContent>
          )}

          {user.role === "ADMIN" && (
            <TabsContent value="users" className="mt-6">
              <UserManagement />
            </TabsContent>
          )}

          <TabsContent value="contracts" className="mt-6">
            <ContractsManagement user={user} />
          </TabsContent>

          {user.role === "ADMIN" && (
            <TabsContent value="analytics" className="mt-6">
              <AnalyticsDashboard user={user} />
            </TabsContent>
          )}

          <TabsContent value="reports" className="mt-6">
            <ExportReports user={user} />
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}
