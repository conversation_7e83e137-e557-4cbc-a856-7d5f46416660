const bcrypt = require("bcryptjs")
const jwt = require("jsonwebtoken")
const { User, AuditLog } = require("../models")
const { logger, logAuditEvent } = require("../utils/logger")

class AuthController {
  async login(req, res) {
    try {
      const { email, password } = req.body

      // Validate input
      if (!email || !password) {
        return res.status(400).json({
          error: "Email et mot de passe requis",
        })
      }

      // Find user by email
      const user = await User.findOne({
        where: { email, isActive: true },
      })

      if (!user) {
        await logAuditEvent("LOGIN_FAILED", null, {
          email,
          reason: "User not found",
          ip: req.ip,
        })
        return res.status(401).json({
          error: "Identifiants invalides",
        })
      }

      // Verify password using motDePasse field
      const isValidPassword = await bcrypt.compare(password, user.motDePasse)
      if (!isValidPassword) {
        await logAuditEvent("LOGIN_FAILED", user.id, {
          email,
          reason: "Invalid password",
          ip: req.ip,
        })
        return res.status(401).json({
          error: "Identifiants invalides",
        })
      }

      // Update last login
      await user.update({ lastLogin: new Date() })

      // Generate token
      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          role: user.role,
        },
        process.env.JWT_SECRET,
        { expiresIn: "24h" },
      )

      await logAuditEvent("LOGIN_SUCCESS", user.id, {
        email,
        ip: req.ip,
      })

      logger.info("User logged in successfully", {
        userId: user.id,
        email: user.email,
      })

      res.json({
        token,
        user: {
          id: user.id,
          nom: user.nom,
          email: user.email,
          role: user.role,
        },
      })
    } catch (error) {
      logger.error("Login error:", error)
      res.status(500).json({
        error: "Erreur interne du serveur",
      })
    }
  }

  async register(req, res) {
    try {
      const { nom, email, password, role = "STANDARD" } = req.body

      // Validate input
      if (!nom || !email || !password) {
        return res.status(400).json({
          error: "Tous les champs sont requis",
        })
      }

      // Check if user exists
      const existingUser = await User.findOne({ where: { email } })
      if (existingUser) {
        return res.status(400).json({
          error: "Un utilisateur avec cet email existe déjà",
        })
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 12)

      // Create user with motDePasse field
      const user = await User.create({
        nom,
        email,
        motDePasse: hashedPassword,
        role,
      })

      await logAuditEvent("USER_CREATED", req.user.id, {
        newUserId: user.id,
        email: user.email,
        role: user.role,
      })

      logger.info("New user created", {
        userId: user.id,
        email: user.email,
        createdBy: req.user.id,
      })

      res.status(201).json({
        message: "Utilisateur créé avec succès",
        user: {
          id: user.id,
          nom: user.nom,
          email: user.email,
          role: user.role,
        },
      })
    } catch (error) {
      logger.error("Registration error:", error)
      res.status(500).json({
        error: "Erreur lors de la création de l'utilisateur",
      })
    }
  }

  async refreshToken(req, res) {
    try {
      const { token } = req.body

      if (!token) {
        return res.status(400).json({
          error: "Token requis",
        })
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET)
      const user = await User.findByPk(decoded.id)

      if (!user || !user.isActive) {
        return res.status(401).json({
          error: "Token invalide",
        })
      }

      const newToken = jwt.sign(
        {
          id: user.id,
          email: user.email,
          role: user.role,
        },
        process.env.JWT_SECRET,
        { expiresIn: "24h" },
      )

      res.json({ token: newToken })
    } catch (error) {
      logger.error("Token refresh error:", error)
      res.status(401).json({
        error: "Token invalide",
      })
    }
  }

  async logout(req, res) {
    try {
      await logAuditEvent("LOGOUT", req.user.id, {
        ip: req.ip,
      })

      logger.info("User logged out", {
        userId: req.user.id,
      })

      res.json({
        message: "Déconnexion réussie",
      })
    } catch (error) {
      logger.error("Logout error:", error)
      res.status(500).json({
        error: "Erreur lors de la déconnexion",
      })
    }
  }

  async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          error: "Mot de passe actuel et nouveau mot de passe requis",
        })
      }

      const user = await User.findByPk(req.user.id)
      const isValidPassword = await bcrypt.compare(currentPassword, user.motDePasse)

      if (!isValidPassword) {
        return res.status(400).json({
          error: "Mot de passe actuel incorrect",
        })
      }

      const hashedNewPassword = await bcrypt.hash(newPassword, 12)
      await user.update({ motDePasse: hashedNewPassword })

      await logAuditEvent("PASSWORD_CHANGED", user.id, {
        ip: req.ip,
      })

      res.json({
        message: "Mot de passe modifié avec succès",
      })
    } catch (error) {
      logger.error("Change password error:", error)
      res.status(500).json({
        error: "Erreur lors du changement de mot de passe",
      })
    }
  }
}

module.exports = new AuthController()
