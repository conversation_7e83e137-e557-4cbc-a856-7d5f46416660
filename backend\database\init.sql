-- GIS Land Management Database Initialization Script
-- This script creates the complete database schema with PostGIS support

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =========================
-- ENUM TYPES
-- =========================
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_documents_type') THEN
        CREATE TYPE "enum_documents_type" AS ENUM ('LOCATION', 'ACHAT', 'SERVITUDE', 'LITIGE');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_documents_statut') THEN
        CREATE TYPE "enum_documents_statut" AS ENUM ('ACTIF', 'EXPIRE', 'EN_RENOUVELLEMENT');
    END IF;
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_utilisateurs_role') THEN
        CREATE TYPE "enum_utilisateurs_role" AS ENUM ('ADMIN', 'JURISTE', 'STANDARD');
    END IF;
END$$;

-- =========================
-- TABLE: Utilisateurs
-- =========================
CREATE TABLE IF NOT EXISTS public."Utilisateurs" (
    "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "nom" VARCHAR NOT NULL,
    "role" "enum_utilisateurs_role" NOT NULL DEFAULT 'STANDARD',
    "email" VARCHAR UNIQUE NOT NULL,
    "motDePasse" VARCHAR NOT NULL,
    "isActive" BOOLEAN DEFAULT TRUE,
    "lastLogin" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- =========================
-- TABLE: Proprietaires
-- =========================
CREATE TABLE IF NOT EXISTS public."Proprietaires" (
    "idProprietaire" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "nom" VARCHAR NOT NULL,
    "prenom" VARCHAR,
    "cin" VARCHAR UNIQUE,
    "contact" VARCHAR,
    "email" VARCHAR,
    "adresse" TEXT,
    "dateNaissance" DATE,
    "nationalite" VARCHAR DEFAULT 'Marocaine',
    "profession" VARCHAR,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- =========================
-- TABLE: Parcelles
-- =========================
CREATE TABLE IF NOT EXISTS public."Parcelles" (
    "idParcelle" VARCHAR PRIMARY KEY,
    "numeroTitreFoncier" VARCHAR UNIQUE NOT NULL,
    "coordonnees" geometry(MultiPolygon, 4326) NOT NULL,
    "superficie" FLOAT,
    "superficieUtile" FLOAT,
    "adresse" TEXT,
    "commune" VARCHAR,
    "province" VARCHAR,
    "region" VARCHAR,
    "zonage" VARCHAR,
    "statut" "enum_documents_statut" DEFAULT 'ACTIF',
    "valeurEstimee" DECIMAL(15,2),
    "observations" TEXT,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW()
);

-- =========================
-- TABLE: Documents
-- =========================
CREATE TABLE IF NOT EXISTS public."Documents" (
    "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "numero" VARCHAR UNIQUE NOT NULL,
    "nom" VARCHAR NOT NULL,
    "type" "enum_documents_type" NOT NULL,
    "statut" "enum_documents_statut" NOT NULL DEFAULT 'ACTIF',
    "dateSignature" DATE,
    "echeance" DATE,
    "fichierPath" VARCHAR,
    "fichierOriginalName" VARCHAR,
    "fichierSize" INTEGER,
    "fichierMimeType" VARCHAR,
    "clauses" TEXT,
    "montant" DECIMAL(15,2),
    "devise" VARCHAR DEFAULT 'MAD',
    "conditions" TEXT,
    "isOcrProcessed" BOOLEAN DEFAULT FALSE,
    "ParcelleIdParcelle" VARCHAR,
    "UtilisateurId" UUID,
    "ProprietaireId" UUID,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_parcelle FOREIGN KEY ("ParcelleIdParcelle")
        REFERENCES public."Parcelles"("idParcelle") ON DELETE SET NULL ON UPDATE CASCADE,

    CONSTRAINT fk_utilisateur FOREIGN KEY ("UtilisateurId")
        REFERENCES public."Utilisateurs"("id") ON DELETE SET NULL ON UPDATE CASCADE,

    CONSTRAINT fk_proprietaire FOREIGN KEY ("ProprietaireId")
        REFERENCES public."Proprietaires"("idProprietaire") ON DELETE SET NULL ON UPDATE CASCADE
);

-- =========================
-- TABLE: OCRTexts
-- =========================
CREATE TABLE IF NOT EXISTS public."OCRTexts" (
    "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "documentId" UUID NOT NULL,
    "extractedText" TEXT,
    "confidence" FLOAT CHECK (confidence >= 0 AND confidence <= 100),
    "language" VARCHAR DEFAULT 'ara+fra',
    "processingTime" INTEGER,
    "textFilePath" VARCHAR,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_document_ocr FOREIGN KEY ("documentId")
        REFERENCES public."Documents"("id") ON DELETE CASCADE
);

-- =========================
-- TABLE: AuditLogs
-- =========================
CREATE TABLE IF NOT EXISTS public."AuditLogs" (
    "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "userId" UUID,
    "action" VARCHAR NOT NULL,
    "entityType" VARCHAR,
    "entityId" VARCHAR,
    "oldValues" JSONB,
    "newValues" JSONB,
    "ipAddress" INET,
    "userAgent" TEXT,
    "details" JSONB,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_audit_user FOREIGN KEY ("userId")
        REFERENCES public."Utilisateurs"("id") ON DELETE SET NULL
);

-- =========================
-- TABLE: Notifications
-- =========================
CREATE TABLE IF NOT EXISTS public."Notifications" (
    "id" UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    "userId" UUID,
    "type" VARCHAR CHECK (type IN ('INFO', 'WARNING', 'ERROR', 'SUCCESS')) DEFAULT 'INFO',
    "title" VARCHAR NOT NULL,
    "message" TEXT NOT NULL,
    "isRead" BOOLEAN DEFAULT FALSE,
    "actionRequired" BOOLEAN DEFAULT FALSE,
    "relatedEntityType" VARCHAR,
    "relatedEntityId" VARCHAR,
    "expiresAt" TIMESTAMPTZ,
    "createdAt" TIMESTAMPTZ DEFAULT NOW(),
    "updatedAt" TIMESTAMPTZ DEFAULT NOW(),

    CONSTRAINT fk_notification_user FOREIGN KEY ("userId")
        REFERENCES public."Utilisateurs"("id") ON DELETE CASCADE
);

-- =========================
-- INDEXES
-- =========================

-- Spatial index for parcelles
CREATE INDEX IF NOT EXISTS idx_parcelles_coordonnees ON public."Parcelles" USING GIST ("coordonnees");

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_documents_parcelle ON public."Documents"("ParcelleIdParcelle");
CREATE INDEX IF NOT EXISTS idx_documents_proprietaire ON public."Documents"("ProprietaireId");
CREATE INDEX IF NOT EXISTS idx_documents_type ON public."Documents"("type");
CREATE INDEX IF NOT EXISTS idx_documents_statut ON public."Documents"("statut");
CREATE INDEX IF NOT EXISTS idx_documents_echeance ON public."Documents"("echeance");
CREATE INDEX IF NOT EXISTS idx_ocrtexts_document ON public."OCRTexts"("documentId");
CREATE INDEX IF NOT EXISTS idx_auditlogs_user ON public."AuditLogs"("userId");
CREATE INDEX IF NOT EXISTS idx_auditlogs_action ON public."AuditLogs"("action");
CREATE INDEX IF NOT EXISTS idx_auditlogs_created ON public."AuditLogs"("createdAt");
CREATE INDEX IF NOT EXISTS idx_notifications_user_read ON public."Notifications"("userId", "isRead");
CREATE INDEX IF NOT EXISTS idx_proprietaires_cin ON public."Proprietaires"("cin");
CREATE INDEX IF NOT EXISTS idx_utilisateurs_email ON public."Utilisateurs"("email");

-- =========================
-- FUNCTIONS
-- =========================

-- Function to get parcels within a bounding box
CREATE OR REPLACE FUNCTION get_parcels_in_bbox(
    min_lon FLOAT,
    min_lat FLOAT,
    max_lon FLOAT,
    max_lat FLOAT
)
RETURNS TABLE (
    parcel_id VARCHAR,
    geometry_json TEXT,
    superficie FLOAT,
    document_count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p."idParcelle",
        ST_AsGeoJSON(p."coordonnees"),
        p.superficie,
        COUNT(d."id")
    FROM public."Parcelles" p
    LEFT JOIN public."Documents" d ON p."idParcelle" = d."ParcelleIdParcelle"
    WHERE ST_Intersects(
        p."coordonnees",
        ST_MakeEnvelope(min_lon, min_lat, max_lon, max_lat, 4326)
    )
    GROUP BY p."idParcelle", p."coordonnees", p.superficie;
END;
$$ LANGUAGE plpgsql;

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."updatedAt" = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =========================
-- TRIGGERS
-- =========================

-- Update triggers for all tables
CREATE TRIGGER update_utilisateurs_updated_at BEFORE UPDATE ON public."Utilisateurs"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_proprietaires_updated_at BEFORE UPDATE ON public."Proprietaires"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_parcelles_updated_at BEFORE UPDATE ON public."Parcelles"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON public."Documents"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ocrtexts_updated_at BEFORE UPDATE ON public."OCRTexts"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON public."Notifications"
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =========================
-- SAMPLE DATA
-- =========================

-- Insert default users (passwords are hashed with bcrypt)
INSERT INTO public."Utilisateurs" ("id", "nom", "role", "email", "motDePasse") VALUES
(uuid_generate_v4(), 'Ahmed Alami', 'ADMIN', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm'), -- admin123
(uuid_generate_v4(), 'Fatima Benali', 'JURISTE', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm'), -- juriste123
(uuid_generate_v4(), 'Omar Tazi', 'STANDARD', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/VcSAg/9qm') -- user123
ON CONFLICT ("email") DO NOTHING;

-- Insert sample proprietaires
INSERT INTO public."Proprietaires" ("idProprietaire", "nom", "prenom", "cin", "contact", "email", "adresse") VALUES
(uuid_generate_v4(), 'Benali', 'Ahmed', 'AB123456', '+************', '<EMAIL>', 'Rue Mohammed V, Casablanca'),
(uuid_generate_v4(), 'Alaoui', 'Fatima', 'FA789012', '+************', '<EMAIL>', 'Avenue Hassan II, Rabat'),
(uuid_generate_v4(), 'Tazi', 'Omar', 'OT345678', '+************', '<EMAIL>', 'Boulevard Zerktouni, Marrakech')
ON CONFLICT ("cin") DO NOTHING;

-- Insert sample parcelles with Moroccan coordinates
INSERT INTO public."Parcelles" ("idParcelle", "numeroTitreFoncier", "coordonnees", "superficie", "adresse", "commune", "province", "region") VALUES
('P-2024-001', 'TF-123456', 
 ST_GeomFromText('MULTIPOLYGON(((-7.5898 33.5731, -7.5888 33.5731, -7.5888 33.5741, -7.5898 33.5741, -7.5898 33.5731)))', 4326),
 2500.0, 'Quartier Maarif, Casablanca', 'Casablanca', 'Casablanca-Settat', 'Casablanca-Settat'),
('P-2024-002', 'TF-789012',
 ST_GeomFromText('MULTIPOLYGON(((-6.8416 34.0209, -6.8406 34.0209, -6.8406 34.0219, -6.8416 34.0219, -6.8416 34.0209)))', 4326),
 1800.0, 'Hay Riad, Rabat', 'Rabat', 'Rabat-Salé-Kénitra', 'Rabat-Salé-Kénitra'),
('P-2024-003', 'TF-345678',
 ST_GeomFromText('MULTIPOLYGON(((-7.9811 31.6295, -7.9801 31.6295, -7.9801 31.6305, -7.9811 31.6305, -7.9811 31.6295)))', 4326),
 3200.0, 'Palmeraie, Marrakech', 'Marrakech', 'Marrakech-Safi', 'Marrakech-Safi')
ON CONFLICT ("idParcelle") DO NOTHING;

-- =========================
-- VIEWS
-- =========================

-- View for parcelles with document count
CREATE OR REPLACE VIEW parcelles_with_documents AS
SELECT 
    p."idParcelle",
    p."numeroTitreFoncier",
    p.superficie,
    p.statut as parcel_status,
    p.adresse,
    p.commune,
    p.province,
    ST_AsGeoJSON(p."coordonnees") as geometry,
    COUNT(d."id") as document_count,
    ARRAY_AGG(d.type) FILTER (WHERE d.type IS NOT NULL) as document_types
FROM public."Parcelles" p
LEFT JOIN public."Documents" d ON p."idParcelle" = d."ParcelleIdParcelle"
GROUP BY p."idParcelle", p."numeroTitreFoncier", p.superficie, p.statut, p.adresse, p.commune, p.province, p."coordonnees";

-- View for documents with related information
CREATE OR REPLACE VIEW documents_with_details AS
SELECT 
    d."id",
    d."numero",
    d."nom",
    d.type,
    d.statut,
    d."dateSignature",
    d.echeance,
    d.montant,
    d.devise,
    d."isOcrProcessed",
    p."idParcelle",
    p."numeroTitreFoncier",
    p.adresse as parcelle_adresse,
    prop."nom" as proprietaire_nom,
    prop."prenom" as proprietaire_prenom,
    prop.cin as proprietaire_cin,
    u."nom" as uploaded_by_nom,
    u.email as uploaded_by_email,
    d."createdAt"
FROM public."Documents" d
LEFT JOIN public."Parcelles" p ON d."ParcelleIdParcelle" = p."idParcelle"
LEFT JOIN public."Proprietaires" prop ON d."ProprietaireId" = prop."idProprietaire"
LEFT JOIN public."Utilisateurs" u ON d."UtilisateurId" = u."id";

-- =========================
-- PERMISSIONS
-- =========================

-- Grant permissions to application user (adjust as needed)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO gis_app_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO gis_app_user;
-- GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO gis_app_user;

COMMENT ON DATABASE current_database() IS 'GIS Land Management System Database with PostGIS support';
