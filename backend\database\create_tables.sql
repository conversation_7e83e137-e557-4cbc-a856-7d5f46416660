-- Create tables for <PERSON><PERSON><PERSON>i database with auto-incrementing IDs

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(255) UNIQUE,
    email VARCHAR(255) NOT NULL UNIQUE,
    "motDePasse" VARCHAR(255) NOT NULL,
    nom VARCHAR(255),
    prenom VARCHAR(255),
    role VARCHAR(50) DEFAULT 'STANDARD' CHECK (role IN ('ADMIN', 'JURIDIQUE', 'STANDARD')),
    "isActive" BOOLEAN DEFAULT true,
    "lastLogin" TIMESTAMP,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Proprietaires table
CREATE TABLE IF NOT EXISTS proprietaires (
    id SERIAL PRIMARY KEY,
    nom VARCHAR(255) NOT NULL,
    prenom VARCHAR(255) NOT NULL,
    cin VARCHAR(255) UNIQUE,
    contact <PERSON><PERSON><PERSON><PERSON>(255),
    email VARCHAR(255),
    adresse TEXT,
    "dateNaissance" DATE,
    nationalite VARCHAR(255) <PERSON>FAULT 'Marocaine',
    profession VARCHAR(255),
    "createdBy" INTEGER REFERENCES users(id),
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Parcelles table
CREATE TABLE IF NOT EXISTS parcelles (
    id SERIAL PRIMARY KEY,
    "numeroTitreFoncier" VARCHAR(255) NOT NULL UNIQUE,
    adresse VARCHAR(255) NOT NULL,
    superficie DECIMAL(10,2) NOT NULL,
    "typeTerrain" VARCHAR(50) DEFAULT 'urbain' CHECK ("typeTerrain" IN ('agricole', 'urbain', 'forestier', 'autre')),
    description TEXT,
    "coordonneesGPS" VARCHAR(255),
    "dateAcquisition" DATE,
    statut VARCHAR(50) DEFAULT 'libre' CHECK (statut IN ('libre', 'occupe', 'en_construction', 'en_litige')),
    "createdBy" INTEGER REFERENCES users(id),
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL CHECK (type IN ('titre_foncier', 'acte_vente', 'bail', 'permis_construction', 'autre')),
    numero VARCHAR(255) NOT NULL,
    "dateEmission" DATE NOT NULL,
    "dateExpiration" DATE,
    description TEXT,
    fichier VARCHAR(255) NOT NULL,
    statut VARCHAR(50) DEFAULT 'valide' CHECK (statut IN ('valide', 'expire', 'en_cours', 'annule')),
    "ProprietaireId" INTEGER REFERENCES proprietaires(id),
    "ParcelleId" INTEGER REFERENCES parcelles(id),
    "createdBy" INTEGER REFERENCES users(id),
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Audit logs table
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    action VARCHAR(255) NOT NULL,
    "entityType" VARCHAR(255),
    "entityId" VARCHAR(255),
    "userId" INTEGER REFERENCES users(id),
    details JSONB,
    "ipAddress" VARCHAR(255),
    "userAgent" TEXT,
    "createdAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_proprietaires_cin ON proprietaires(cin);
CREATE INDEX IF NOT EXISTS idx_parcelles_titre ON parcelles("numeroTitreFoncier");
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_entity ON audit_logs("entityType", "entityId");
CREATE INDEX IF NOT EXISTS idx_audit_logs_user ON audit_logs("userId");
CREATE INDEX IF NOT EXISTS idx_audit_logs_created ON audit_logs("createdAt");
