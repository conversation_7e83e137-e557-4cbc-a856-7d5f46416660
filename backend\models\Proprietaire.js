const { Model, DataTypes } = require("sequelize")
const sequelize = require("../config/database")

class <PERSON><PERSON><PERSON><PERSON> extends Model {}

Proprietaire.init(
  {
    idProprietaire: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    nom: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    prenom: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    cin: {
      type: DataTypes.STRING,
      allowNull: true,
      unique: true,
    },
    contact: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: true,
      validate: {
        isEmail: true,
      },
    },
    adresse: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    dateNaissance: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    nationalite: {
      type: DataTypes.STRING,
      defaultValue: "Marocaine",
    },
    profession: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "users",
        key: "idUser",
      },
    },
  },
  {
    sequelize,
    modelName: "Proprietaire",
    tableName: "proprietaires",
    timestamps: true,
  }
)

module.exports = Proprietaire 