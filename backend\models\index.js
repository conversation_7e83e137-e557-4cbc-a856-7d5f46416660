const sequelize = require("../config/database")

// Import individual models
const User = require("./User")
const Proprietaire = require("./Proprietaire")
const Parcelle = require("./Parcelle")
const Document = require("./Document")
const AuditLog = require("./AuditLog")









// Define Associations
User.hasMany(Proprietaire, { foreignKey: "createdBy" })
Proprietaire.belongsTo(User, { foreignKey: "createdBy" })

User.hasMany(Document, { foreignKey: "createdBy" })
Document.belongsTo(User, { foreignKey: "createdBy" })

User.hasMany(Parcelle, { foreignKey: "createdBy" })
Parcelle.belongsTo(User, { foreignKey: "createdBy" })

Proprietaire.hasMany(Document, { foreignKey: "ProprietaireId" })
Document.belongsTo(Proprietaire, { foreignKey: "ProprietaireId" })

Parcelle.hasMany(Document, { foreignKey: "ParcelleId" })
Document.belongsTo(Pa<PERSON>elle, { foreignKey: "ParcelleId" })

User.hasMany(AuditLog, { foreignKey: "userId" })
AuditLog.belongsTo(User, { foreignKey: "userId" })

module.exports = {
  sequelize,
  User,
  Proprietaire,
  Parcelle,
  Document,
  AuditLog,
}
