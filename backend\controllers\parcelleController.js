const { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OCRT<PERSON>t } = require("../models")
const { Op, sequelize } = require("sequelize")
const { logger, logAuditEvent } = require("../utils/logger")

class ParcelleController {
  async getAllParcelles(req, res) {
    try {
      const { page = 1, limit = 10, search, statut, commune, province, minSuperficie, maxSuperficie } = req.query

      const offset = (page - 1) * limit
      const whereClause = {}

      // Build search conditions
      if (search) {
        whereClause[Op.or] = [
          { idParcelle: { [Op.iLike]: `%${search}%` } },
          { numeroTitreFoncier: { [Op.iLike]: `%${search}%` } },
          { adresse: { [Op.iLike]: `%${search}%` } },
        ]
      }

      if (statut) {
        whereClause.statut = statut
      }

      if (commune) {
        whereClause.commune = { [Op.iLike]: `%${commune}%` }
      }

      if (province) {
        whereClause.province = { [Op.iLike]: `%${province}%` }
      }

      if (minSuperficie || maxSuperficie) {
        whereClause.superficie = {}
        if (minSuperficie) whereClause.superficie[Op.gte] = Number.parseFloat(minSuperficie)
        if (maxSuperficie) whereClause.superficie[Op.lte] = Number.parseFloat(maxSuperficie)
      }

      const { count, rows: parcelles } = await Parcelle.findAndCountAll({
        where: whereClause,
        include: [
          {
            model: Document,
            as: "documents",
            include: [
              {
                model: Proprietaire,
                as: "proprietaire",
              },
            ],
          },
        ],
        limit: Number.parseInt(limit),
        offset: Number.parseInt(offset),
        order: [["createdAt", "DESC"]],
      })

      // Transform coordinates for frontend
      const transformedParcelles = parcelles.map((parcelle) => {
        const parcelleData = parcelle.toJSON()
        if (parcelleData.coordonnees) {
          // Convert PostGIS geometry to GeoJSON
          parcelleData.coordinates = parcelleData.coordonnees.coordinates
        }
        return parcelleData
      })

      res.json({
        parcelles: transformedParcelles,
        pagination: {
          currentPage: Number.parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: Number.parseInt(limit),
        },
      })
    } catch (error) {
      logger.error("Get parcelles error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des parcelles",
      })
    }
  }

  async getParcelleById(req, res) {
    try {
      const { id } = req.params

      const parcelle = await Parcelle.findByPk(id, {
        include: [
          {
            model: Document,
            as: "documents",
            include: [
              {
                model: Proprietaire,
                as: "proprietaire",
              },
              {
                model: OCRText,
                as: "ocrText",
              },
            ],
          },
        ],
      })

      if (!parcelle) {
        return res.status(404).json({
          error: "Parcelle non trouvée",
        })
      }

      // Transform coordinates for frontend
      const parcelleData = parcelle.toJSON()
      if (parcelleData.coordonnees) {
        parcelleData.coordinates = parcelleData.coordonnees.coordinates
      }

      res.json(parcelleData)
    } catch (error) {
      logger.error("Get parcelle by ID error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération de la parcelle",
      })
    }
  }

  async createParcelle(req, res) {
    try {
      const {
        idParcelle,
        numeroTitreFoncier,
        coordonnees,
        superficie,
        superficieUtile,
        adresse,
        commune,
        province,
        region,
        zonage,
        valeurEstimee,
        observations,
      } = req.body

      // Validate required fields
      if (!idParcelle || !numeroTitreFoncier || !coordonnees) {
        return res.status(400).json({
          error: "Champs requis manquants: idParcelle, numeroTitreFoncier, coordonnees",
        })
      }

      // Check if parcelle already exists
      const existingParcelle = await Parcelle.findOne({
        where: {
          [Op.or]: [{ idParcelle }, { numeroTitreFoncier }],
        },
      })

      if (existingParcelle) {
        return res.status(400).json({
          error: "Une parcelle avec cet ID ou numéro de titre foncier existe déjà",
        })
      }

      // Convert coordinates to PostGIS format if needed
      let geometryData = coordonnees
      if (typeof coordonnees === "object" && coordonnees.type === "MultiPolygon") {
        geometryData = {
          type: "MultiPolygon",
          coordinates: coordonnees.coordinates,
        }
      }

      const parcelle = await Parcelle.create({
        idParcelle,
        numeroTitreFoncier,
        coordonnees: geometryData,
        superficie,
        superficieUtile,
        adresse,
        commune,
        province,
        region,
        zonage,
        valeurEstimee,
        observations,
      })

      await logAuditEvent("PARCELLE_CREATED", req.user.id, {
        parcelleId: parcelle.idParcelle,
        numeroTitreFoncier: parcelle.numeroTitreFoncier,
      })

      logger.info("Parcelle created", {
        parcelleId: parcelle.idParcelle,
        createdBy: req.user.id,
      })

      res.status(201).json({
        message: "Parcelle créée avec succès",
        parcelle,
      })
    } catch (error) {
      logger.error("Create parcelle error:", error)
      res.status(500).json({
        error: "Erreur lors de la création de la parcelle",
      })
    }
  }

  async updateParcelle(req, res) {
    try {
      const { id } = req.params
      const updateData = req.body

      const parcelle = await Parcelle.findByPk(id)
      if (!parcelle) {
        return res.status(404).json({
          error: "Parcelle non trouvée",
        })
      }

      const oldValues = parcelle.toJSON()
      await parcelle.update(updateData)

      await logAuditEvent("PARCELLE_UPDATED", req.user.id, {
        parcelleId: parcelle.idParcelle,
        oldValues,
        newValues: updateData,
      })

      logger.info("Parcelle updated", {
        parcelleId: parcelle.idParcelle,
        updatedBy: req.user.id,
      })

      res.json({
        message: "Parcelle mise à jour avec succès",
        parcelle,
      })
    } catch (error) {
      logger.error("Update parcelle error:", error)
      res.status(500).json({
        error: "Erreur lors de la mise à jour de la parcelle",
      })
    }
  }

  async deleteParcelle(req, res) {
    try {
      const { id } = req.params

      const parcelle = await Parcelle.findByPk(id)
      if (!parcelle) {
        return res.status(404).json({
          error: "Parcelle non trouvée",
        })
      }

      // Check if parcelle has associated documents
      const documentsCount = await Document.count({
        where: { ParcelleIdParcelle: id },
      })

      if (documentsCount > 0) {
        return res.status(400).json({
          error: "Impossible de supprimer une parcelle avec des documents associés",
        })
      }

      await parcelle.destroy()

      await logAuditEvent("PARCELLE_DELETED", req.user.id, {
        parcelleId: parcelle.idParcelle,
        numeroTitreFoncier: parcelle.numeroTitreFoncier,
      })

      logger.info("Parcelle deleted", {
        parcelleId: parcelle.idParcelle,
        deletedBy: req.user.id,
      })

      res.json({
        message: "Parcelle supprimée avec succès",
      })
    } catch (error) {
      logger.error("Delete parcelle error:", error)
      res.status(500).json({
        error: "Erreur lors de la suppression de la parcelle",
      })
    }
  }

  async getParcellesInBounds(req, res) {
    try {
      const { minLat, minLng, maxLat, maxLng } = req.query

      if (!minLat || !minLng || !maxLat || !maxLng) {
        return res.status(400).json({
          error: "Coordonnées de délimitation requises",
        })
      }

      // Use raw query for spatial operations
      const parcelles = await sequelize.query(
        `
        SELECT 
          p."idParcelle",
          p."numeroTitreFoncier",
          p.superficie,
          p.adresse,
          p.commune,
          p.statut,
          ST_AsGeoJSON(p.coordonnees) as geometry,
          COUNT(d.id) as document_count
        FROM "Parcelles" p
        LEFT JOIN "Documents" d ON p."idParcelle" = d."ParcelleIdParcelle"
        WHERE ST_Intersects(
          p.coordonnees,
          ST_MakeEnvelope($1, $2, $3, $4, 4326)
        )
        GROUP BY p."idParcelle", p."numeroTitreFoncier", p.superficie, p.adresse, p.commune, p.statut, p.coordonnees
      `,
        {
          bind: [minLng, minLat, maxLng, maxLat],
          type: sequelize.QueryTypes.SELECT,
        },
      )

      res.json(parcelles)
    } catch (error) {
      logger.error("Get parcelles in bounds error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des parcelles dans la zone",
      })
    }
  }

  async getParcelleStatistics(req, res) {
    try {
      const stats = await Parcelle.findAll({
        attributes: [
          "statut",
          [sequelize.fn("COUNT", sequelize.col("idParcelle")), "count"],
          [sequelize.fn("SUM", sequelize.col("superficie")), "totalSuperficie"],
          [sequelize.fn("AVG", sequelize.col("superficie")), "superficieMoyenne"],
        ],
        group: ["statut"],
      })

      const totalParcelles = await Parcelle.count()
      const totalSuperficie = await Parcelle.sum("superficie")

      res.json({
        totalParcelles,
        totalSuperficie,
        statistiquesParStatut: stats,
      })
    } catch (error) {
      logger.error("Get parcelle statistics error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des statistiques",
      })
    }
  }
}

module.exports = new ParcelleController()
