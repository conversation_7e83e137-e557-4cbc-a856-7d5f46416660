const express = require('express');
const router = express.Router();
const { authenticateToken, requireRole } = require('../middleware/auth');
const { AuditLog, User } = require('../models');
const { Op } = require('sequelize');

// Get all audit logs (admin only)
router.get('/', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { page = 1, limit = 50, action, user, startDate, endDate } = req.query;
    const where = {};

    // Apply filters if provided
    if (action) where.action = action;
    if (user) where.userId = user;
    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) where.createdAt[Op.gte] = new Date(startDate);
      if (endDate) where.createdAt[Op.lte] = new Date(endDate);
    }

    const { count, rows: auditLogs } = await AuditLog.findAndCountAll({
      where,
      include: [{
        model: User,
        attributes: ['nom', 'prenom', 'email']
      }],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    res.json({
      auditLogs,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      totalLogs: count
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get audit log by ID (admin only)
router.get('/:id', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const auditLog = await AuditLog.findByPk(req.params.id, {
      include: [{
        model: User,
        attributes: ['nom', 'prenom', 'email']
      }]
    });

    if (!auditLog) {
      return res.status(404).json({ message: 'Journal d\'audit non trouvé' });
    }

    res.json(auditLog);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get audit logs for specific entity (admin only)
router.get('/entity/:entityType/:entityId', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const { entityType, entityId } = req.params;
    const { page = 1, limit = 50 } = req.query;

    const { count, rows: auditLogs } = await AuditLog.findAndCountAll({
      where: {
        entityType,
        entityId
      },
      include: [{
        model: User,
        attributes: ['nom', 'prenom', 'email']
      }],
      order: [['createdAt', 'DESC']],
      limit: parseInt(limit),
      offset: (parseInt(page) - 1) * parseInt(limit)
    });

    res.json({
      auditLogs,
      totalPages: Math.ceil(count / limit),
      currentPage: parseInt(page),
      totalLogs: count
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get audit statistics (admin only)
router.get('/statistics', authenticateToken, requireRole(['ADMIN']), async (req, res) => {
  try {
    const totalLogs = await AuditLog.count();
    const recentLogs = await AuditLog.count({
      where: {
        createdAt: {
          [Op.gte]: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      }
    });

    res.json({
      totalLogs,
      recentLogs
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router; 