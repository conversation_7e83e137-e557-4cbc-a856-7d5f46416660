const { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ument, <PERSON><PERSON><PERSON> } = require("../models")
const { Op, sequelize } = require("sequelize")
const { logger, logAuditEvent } = require("../utils/logger")

class ProprietaireController {
  async getAllProprietaires(req, res) {
    try {
      const { page = 1, limit = 10, search, cin } = req.query

      const offset = (page - 1) * limit
      const whereClause = {}

      // Build search conditions
      if (search) {
        whereClause[Op.or] = [
          { nom: { [Op.iLike]: `%${search}%` } },
          { prenom: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { adresse: { [Op.iLike]: `%${search}%` } },
        ]
      }

      if (cin) {
        whereClause.cin = { [Op.iLike]: `%${cin}%` }
      }

      const { count, rows: proprietaires } = await Proprietaire.findAndCountA<PERSON>({
        where: whereClause,
        include: [
          {
            model: Document,
            as: "documents",
            include: [
              {
                model: Parcelle,
                as: "parcelle",
                attributes: ["idParcelle", "numeroTitreFoncier", "adresse"],
              },
            ],
          },
        ],
        limit: Number.parseInt(limit),
        offset: Number.parseInt(offset),
        order: [["createdAt", "DESC"]],
      })

      res.json({
        proprietaires,
        pagination: {
          currentPage: Number.parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: Number.parseInt(limit),
        },
      })
    } catch (error) {
      logger.error("Get proprietaires error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des propriétaires",
      })
    }
  }

  async getProprietaireById(req, res) {
    try {
      const { id } = req.params

      const proprietaire = await Proprietaire.findByPk(id, {
        include: [
          {
            model: Document,
            as: "documents",
            include: [
              {
                model: Parcelle,
                as: "parcelle",
                attributes: ["idParcelle", "numeroTitreFoncier", "adresse", "superficie"],
              },
            ],
          },
        ],
      })

      if (!proprietaire) {
        return res.status(404).json({
          error: "Propriétaire non trouvé",
        })
      }

      res.json(proprietaire)
    } catch (error) {
      logger.error("Get proprietaire by ID error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération du propriétaire",
      })
    }
  }

  async createProprietaire(req, res) {
    try {
      const {
        nom,
        prenom,
        cin,
        contact,
        email,
        adresse,
        dateNaissance,
        nationalite = "Marocaine",
        profession,
      } = req.body

      // Validate required fields
      if (!nom) {
        return res.status(400).json({
          error: "Le nom est requis",
        })
      }

      // Check if CIN already exists
      if (cin) {
        const existingProprietaire = await Proprietaire.findOne({
          where: { cin },
        })

        if (existingProprietaire) {
          return res.status(400).json({
            error: "Un propriétaire avec ce CIN existe déjà",
          })
        }
      }

      const proprietaire = await Proprietaire.create({
        nom,
        prenom,
        cin,
        contact,
        email,
        adresse,
        dateNaissance,
        nationalite,
        profession,
      })

      await logAuditEvent("PROPRIETAIRE_CREATED", req.user.id, {
        proprietaireId: proprietaire.idProprietaire,
        nom: proprietaire.nom,
        prenom: proprietaire.prenom,
        cin: proprietaire.cin,
      })

      logger.info("Proprietaire created", {
        proprietaireId: proprietaire.idProprietaire,
        createdBy: req.user.id,
      })

      res.status(201).json({
        message: "Propriétaire créé avec succès",
        proprietaire,
      })
    } catch (error) {
      logger.error("Create proprietaire error:", error)
      res.status(500).json({
        error: "Erreur lors de la création du propriétaire",
      })
    }
  }

  async updateProprietaire(req, res) {
    try {
      const { id } = req.params
      const updateData = req.body

      const proprietaire = await Proprietaire.findByPk(id)
      if (!proprietaire) {
        return res.status(404).json({
          error: "Propriétaire non trouvé",
        })
      }

      // Check if CIN is being updated and doesn't conflict
      if (updateData.cin && updateData.cin !== proprietaire.cin) {
        const existingProprietaire = await Proprietaire.findOne({
          where: {
            cin: updateData.cin,
            idProprietaire: { [Op.ne]: id },
          },
        })

        if (existingProprietaire) {
          return res.status(400).json({
            error: "Un propriétaire avec ce CIN existe déjà",
          })
        }
      }

      const oldValues = proprietaire.toJSON()
      await proprietaire.update(updateData)

      await logAuditEvent("PROPRIETAIRE_UPDATED", req.user.id, {
        proprietaireId: proprietaire.idProprietaire,
        oldValues,
        newValues: updateData,
      })

      logger.info("Proprietaire updated", {
        proprietaireId: proprietaire.idProprietaire,
        updatedBy: req.user.id,
      })

      res.json({
        message: "Propriétaire mis à jour avec succès",
        proprietaire,
      })
    } catch (error) {
      logger.error("Update proprietaire error:", error)
      res.status(500).json({
        error: "Erreur lors de la mise à jour du propriétaire",
      })
    }
  }

  async deleteProprietaire(req, res) {
    try {
      const { id } = req.params

      const proprietaire = await Proprietaire.findByPk(id)
      if (!proprietaire) {
        return res.status(404).json({
          error: "Propriétaire non trouvé",
        })
      }

      // Check if proprietaire has associated documents
      const documentsCount = await Document.count({
        where: { ProprietaireId: id },
      })

      if (documentsCount > 0) {
        return res.status(400).json({
          error: "Impossible de supprimer un propriétaire avec des documents associés",
        })
      }

      await proprietaire.destroy()

      await logAuditEvent("PROPRIETAIRE_DELETED", req.user.id, {
        proprietaireId: proprietaire.idProprietaire,
        nom: proprietaire.nom,
        prenom: proprietaire.prenom,
        cin: proprietaire.cin,
      })

      logger.info("Proprietaire deleted", {
        proprietaireId: proprietaire.idProprietaire,
        deletedBy: req.user.id,
      })

      res.json({
        message: "Propriétaire supprimé avec succès",
      })
    } catch (error) {
      logger.error("Delete proprietaire error:", error)
      res.status(500).json({
        error: "Erreur lors de la suppression du propriétaire",
      })
    }
  }

  async getProprietaireStatistics(req, res) {
    try {
      const totalProprietaires = await Proprietaire.count()

      const proprietairesWithDocuments = await Proprietaire.count({
        include: [
          {
            model: Document,
            as: "documents",
            required: true,
          },
        ],
      })

      const proprietairesByNationalite = await Proprietaire.findAll({
        attributes: ["nationalite", [sequelize.fn("COUNT", sequelize.col("idProprietaire")), "count"]],
        group: ["nationalite"],
      })

      const recentProprietaires = await Proprietaire.findAll({
        where: {
          createdAt: {
            [Op.gte]: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Last 30 days
          },
        },
        order: [["createdAt", "DESC"]],
        limit: 10,
      })

      res.json({
        totalProprietaires,
        proprietairesWithDocuments,
        proprietairesSansDocuments: totalProprietaires - proprietairesWithDocuments,
        proprietairesByNationalite,
        recentProprietaires,
      })
    } catch (error) {
      logger.error("Get proprietaire statistics error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des statistiques",
      })
    }
  }
}

module.exports = new ProprietaireController()
