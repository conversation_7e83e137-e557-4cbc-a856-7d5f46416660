-- Insert sample data for Chergui database

-- Insert users (passwords are hashed with bcrypt for 'admin123', 'juriste123', 'user123')
INSERT INTO users (username, nom, email, "motDePasse", role) VALUES
('admin', '<PERSON> SMIDA', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODzm4su6', 'ADMIN'),
('juriste', '<PERSON><PERSON> CHAARI', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODzm4su6', 'JURIDIQUE'),
('user', '<PERSON><PERSON>', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODzm4su6', 'STANDARD');

-- Insert proprietaires
INSERT INTO proprietaires (nom, prenom, cin, contact, email, adresse, nationalite, profession, "createdBy") VALUES
('Benali', 'Ahmed', 'AB123456', '+212600000001', '<EMAIL>', 'Rue Mohammed V, Casablanca', 'Marocaine', 'Ingénieur', 1),
('Alaoui', 'Fatima', 'FA789012', '+212600000002', '<EMAIL>', 'Avenue Hassan II, Rabat', 'Marocaine', 'Médecin', 1),
('Tazi', 'Omar', 'OT345678', '+212600000003', '<EMAIL>', 'Boulevard Zerktouni, Marrakech', 'Marocaine', 'Commerçant', 1);

-- Insert parcelles with real Moroccan coordinates
INSERT INTO parcelles ("numeroTitreFoncier", adresse, superficie, "typeTerrain", description, "coordonneesGPS", statut, "createdBy") VALUES
('TF-123456', 'Kerkennah, Sfax', 2500.00, 'urbain', 'Terrain résidentiel avec vue sur mer', '33.5731,-7.5898', 'libre', 1),
('TF-789012', 'Chergui, Sfax', 1800.00, 'urbain', 'Terrain commercial en centre ville', '34.0209,-6.8416', 'libre', 1),
('TF-345678', 'Kerkennah, Sfax', 3200.00, 'urbain', 'Terrain touristique avec accès plage', '31.6295,-7.9811', 'libre', 1);

-- Insert documents
INSERT INTO documents (numero, type, statut, "dateEmission", "dateExpiration", description, fichier, "ParcelleId", "ProprietaireId", "createdBy") VALUES
('DOC-2024-001', 'bail', 'valide', '2024-01-15', '2025-01-15', 'Contrat de location résidentielle avec option d''achat après 2 ans', 'contrat_location_001.pdf', 1, 1, 1),
('DOC-2024-002', 'acte_vente', 'valide', '2024-01-20', '2024-12-31', 'Vente définitive avec garantie décennale et clause de non-concurrence', 'acte_vente_002.pdf', 2, 2, 1),
('DOC-2024-003', 'autre', 'valide', '2023-06-10', '2024-06-10', 'Droit de passage pour accès à la voie publique avec compensation financière', 'servitude_003.pdf', 3, 3, 1);
