-- Insert sample data for Chergui database

-- First, let's check if users table is empty and insert users
-- Insert users (passwords are hashed with bcrypt for 'admin123', 'juriste123', 'user123')
INSERT INTO users (username, nom, email, "motDePasse", role) VALUES
('admin', '<PERSON> SMIDA', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODzm4su6', 'ADMIN'),
('juriste', 'Mehdi CHAARI', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODzm4su6', 'JURIDIQUE'),
('user', '<PERSON><PERSON>HEBOU', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hODzm4su6', 'STANDARD')
ON CONFLICT (email) DO NOTHING;

-- Insert proprietaires (using the actual user ID from the admin user)
INSERT INTO proprietaires (nom, prenom, cin, contact, email, adresse, nationalite, profession, "createdBy") VALUES
('LADJIMI', 'Ghazi', 'AB123456', '+21620000001', '<EMAIL>', 'Rue Mohammed V, Tunis', 'Tunisien', 'Ingénieur', (SELECT id FROM users WHERE email = '<EMAIL>')),
('Alaoui', 'Fatima', 'FA789012', '+21620000002', '<EMAIL>', 'Avenue Liberté, Tunis', 'Tunisien', 'Médecin', (SELECT id FROM users WHERE email = '<EMAIL>')),
('Tazi', 'Omar', 'OT345678', '+21620000003', '<EMAIL>', 'Ariena, Tunis', 'Tunisien', 'Commerçant', (SELECT id FROM users WHERE email = '<EMAIL>'));

-- Insert parcelles with real Tunisian coordinates
INSERT INTO parcelles ("numeroTitreFoncier", adresse, superficie, "typeTerrain", description, "coordonneesGPS", statut, "createdBy") VALUES
('TF-123456', 'Kerkennah, Sfax', 2500.00, 'urbain', 'Terrain résidentiel avec vue sur mer', '34.7406,10.7603', 'libre', (SELECT id FROM users WHERE email = '<EMAIL>')),
('TF-789012', 'Chergui, Sfax', 1800.00, 'urbain', 'Terrain commercial en centre ville', '34.7406,10.7603', 'libre', (SELECT id FROM users WHERE email = '<EMAIL>')),
('TF-345678', 'Kerkennah, Sfax', 3200.00, 'urbain', 'Terrain touristique avec accès plage', '34.7406,10.7603', 'libre', (SELECT id FROM users WHERE email = '<EMAIL>'));

-- Insert documents (using subqueries to get the correct IDs)
INSERT INTO documents (numero, type, statut, "dateEmission", "dateExpiration", description, fichier, "ParcelleId", "ProprietaireId", "createdBy") VALUES
('DOC-2024-001', 'bail', 'valide', '2024-01-15', '2025-01-15', 'Contrat de location', 'contrat_location_001.pdf',
 (SELECT id FROM parcelles WHERE "numeroTitreFoncier" = 'TF-123456'),
 (SELECT id FROM proprietaires WHERE cin = 'AB123456'),
 (SELECT id FROM users WHERE email = '<EMAIL>')),
('DOC-2024-002', 'acte_vente', 'valide', '2024-01-20', '2024-12-31', 'Contrat de location de terrain', 'acte_vente_002.pdf',
 (SELECT id FROM parcelles WHERE "numeroTitreFoncier" = 'TF-789012'),
 (SELECT id FROM proprietaires WHERE cin = 'FA789012'),
 (SELECT id FROM users WHERE email = '<EMAIL>')),
('DOC-2024-003', 'autre', 'valide', '2023-06-10', '2024-06-10', 'Droit de passage pour accès à la voie publique avec compensation financière', 'servitude_003.pdf',
 (SELECT id FROM parcelles WHERE "numeroTitreFoncier" = 'TF-345678'),
 (SELECT id FROM proprietaires WHERE cin = 'OT345678'),
 (SELECT id FROM users WHERE email = '<EMAIL>'));
