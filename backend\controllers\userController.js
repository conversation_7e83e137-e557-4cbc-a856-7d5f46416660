const { User } = require("../models")
const { Op } = require("sequelize")
const { logger, logAuditEvent } = require("../utils/logger")
const bcrypt = require("bcryptjs")
const jwt = require("jsonwebtoken")

class UserController {
  async getAllUsers(req, res) {
    try {
      const { page = 1, limit = 10, search, role } = req.query

      const offset = (page - 1) * limit
      const whereClause = {}

      // Build search conditions
      if (search) {
        whereClause[Op.or] = [
          { username: { [Op.iLike]: `%${search}%` } },
          { email: { [Op.iLike]: `%${search}%` } },
          { nom: { [Op.iLike]: `%${search}%` } },
          { prenom: { [Op.iLike]: `%${search}%` } },
        ]
      }

      if (role) {
        whereClause.role = role
      }

      const { count, rows: users } = await User.findAndCountAll({
        where: where<PERSON>lause,
        attributes: { exclude: ["password"] },
        limit: Number.parseInt(limit),
        offset: Number.parseInt(offset),
        order: [["createdAt", "DESC"]],
      })

      res.json({
        users,
        pagination: {
          currentPage: Number.parseInt(page),
          totalPages: Math.ceil(count / limit),
          totalItems: count,
          itemsPerPage: Number.parseInt(limit),
        },
      })
    } catch (error) {
      logger.error("Get users error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération des utilisateurs",
      })
    }
  }

  async getUserById(req, res) {
    try {
      const { id } = req.params

      const user = await User.findByPk(id, {
        attributes: { exclude: ["password"] },
      })

      if (!user) {
        return res.status(404).json({
          error: "Utilisateur non trouvé",
        })
      }

      res.json(user)
    } catch (error) {
      logger.error("Get user by ID error:", error)
      res.status(500).json({
        error: "Erreur lors de la récupération de l'utilisateur",
      })
    }
  }

  async createUser(req, res) {
    try {
      const {
        username,
        email,
        password,
        nom,
        prenom,
        role = "user",
        isActive = true,
      } = req.body

      // Validate required fields
      if (!username || !email || !password) {
        return res.status(400).json({
          error: "Le nom d'utilisateur, l'email et le mot de passe sont requis",
        })
      }

      // Check if username or email already exists
      const existingUser = await User.findOne({
        where: {
          [Op.or]: [{ username }, { email }],
        },
      })

      if (existingUser) {
        return res.status(400).json({
          error: "Un utilisateur avec ce nom d'utilisateur ou cet email existe déjà",
        })
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10)

      const user = await User.create({
        username,
        email,
        password: hashedPassword,
        nom,
        prenom,
        role,
        isActive,
      })

      await logAuditEvent("USER_CREATED", req.user.id, {
        userId: user.idUser,
        username: user.username,
        email: user.email,
        role: user.role,
      })

      logger.info("User created", {
        userId: user.idUser,
        createdBy: req.user.id,
      })

      // Remove password from response
      const userResponse = user.toJSON()
      delete userResponse.password

      res.status(201).json({
        message: "Utilisateur créé avec succès",
        user: userResponse,
      })
    } catch (error) {
      logger.error("Create user error:", error)
      res.status(500).json({
        error: "Erreur lors de la création de l'utilisateur",
      })
    }
  }

  async updateUser(req, res) {
    try {
      const { id } = req.params
      const updateData = req.body

      const user = await User.findByPk(id)
      if (!user) {
        return res.status(404).json({
          error: "Utilisateur non trouvé",
        })
      }

      // Check if username or email is being updated and doesn't conflict
      if (updateData.username || updateData.email) {
        const existingUser = await User.findOne({
          where: {
            [Op.or]: [
              { username: updateData.username },
              { email: updateData.email },
            ],
            idUser: { [Op.ne]: id },
          },
        })

        if (existingUser) {
          return res.status(400).json({
            error: "Un utilisateur avec ce nom d'utilisateur ou cet email existe déjà",
          })
        }
      }

      // Hash password if it's being updated
      if (updateData.password) {
        updateData.password = await bcrypt.hash(updateData.password, 10)
      }

      const oldValues = user.toJSON()
      await user.update(updateData)

      await logAuditEvent("USER_UPDATED", req.user.id, {
        userId: user.idUser,
        oldValues,
        newValues: updateData,
      })

      logger.info("User updated", {
        userId: user.idUser,
        updatedBy: req.user.id,
      })

      // Remove password from response
      const userResponse = user.toJSON()
      delete userResponse.password

      res.json({
        message: "Utilisateur mis à jour avec succès",
        user: userResponse,
      })
    } catch (error) {
      logger.error("Update user error:", error)
      res.status(500).json({
        error: "Erreur lors de la mise à jour de l'utilisateur",
      })
    }
  }

  async deleteUser(req, res) {
    try {
      const { id } = req.params

      const user = await User.findByPk(id)
      if (!user) {
        return res.status(404).json({
          error: "Utilisateur non trouvé",
        })
      }

      // Prevent self-deletion
      if (user.idUser === req.user.id) {
        return res.status(400).json({
          error: "Vous ne pouvez pas supprimer votre propre compte",
        })
      }

      await user.destroy()

      await logAuditEvent("USER_DELETED", req.user.id, {
        userId: user.idUser,
        username: user.username,
        email: user.email,
      })

      logger.info("User deleted", {
        userId: user.idUser,
        deletedBy: req.user.id,
      })

      res.json({
        message: "Utilisateur supprimé avec succès",
      })
    } catch (error) {
      logger.error("Delete user error:", error)
      res.status(500).json({
        error: "Erreur lors de la suppression de l'utilisateur",
      })
    }
  }

  async login(req, res) {
    try {
      const { username, password } = req.body

      if (!username || !password) {
        return res.status(400).json({
          error: "Le nom d'utilisateur et le mot de passe sont requis",
        })
      }

      const user = await User.findOne({
        where: { username },
      })

      if (!user || !user.isActive) {
        return res.status(401).json({
          error: "Nom d'utilisateur ou mot de passe incorrect",
        })
      }

      const isValidPassword = await bcrypt.compare(password, user.password)
      if (!isValidPassword) {
        return res.status(401).json({
          error: "Nom d'utilisateur ou mot de passe incorrect",
        })
      }

      const token = jwt.sign(
        {
          id: user.idUser,
          username: user.username,
          role: user.role,
        },
        process.env.JWT_SECRET,
        { expiresIn: "24h" }
      )

      await logAuditEvent("USER_LOGIN", user.idUser, {
        userId: user.idUser,
        username: user.username,
      })

      logger.info("User logged in", {
        userId: user.idUser,
      })

      res.json({
        message: "Connexion réussie",
        token,
        user: {
          id: user.idUser,
          username: user.username,
          email: user.email,
          nom: user.nom,
          prenom: user.prenom,
          role: user.role,
        },
      })
    } catch (error) {
      logger.error("Login error:", error)
      res.status(500).json({
        error: "Erreur lors de la connexion",
      })
    }
  }

  async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body
      const userId = req.user.id

      const user = await User.findByPk(userId)
      if (!user) {
        return res.status(404).json({
          error: "Utilisateur non trouvé",
        })
      }

      const isValidPassword = await bcrypt.compare(currentPassword, user.password)
      if (!isValidPassword) {
        return res.status(401).json({
          error: "Mot de passe actuel incorrect",
        })
      }

      const hashedPassword = await bcrypt.hash(newPassword, 10)
      await user.update({ password: hashedPassword })

      await logAuditEvent("PASSWORD_CHANGED", userId, {
        userId: user.idUser,
        username: user.username,
      })

      logger.info("Password changed", {
        userId: user.idUser,
      })

      res.json({
        message: "Mot de passe modifié avec succès",
      })
    } catch (error) {
      logger.error("Change password error:", error)
      res.status(500).json({
        error: "Erreur lors du changement de mot de passe",
      })
    }
  }
}

module.exports = new UserController() 