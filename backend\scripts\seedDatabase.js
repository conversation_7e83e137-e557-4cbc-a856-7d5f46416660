const bcrypt = require("bcryptjs")
const { sequelize, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Document } = require("../models")
require("dotenv").config()

async function seedDatabase() {
  try {
    console.log("🌱 Starting database seeding...")

    // Sync database
    await sequelize.sync({ force: true })
    console.log("✅ Database synced")

    // Create default users
    const hashedPassword = await bcrypt.hash("admin123", 12)

    const users = await User.bulkCreate([
      {
        username: "admin",
        nom: "Adam SMIDA",
        email: "<EMAIL>",
        motDePasse: hashedPassword,
        role: "ADMIN",
      },
      {
        username: "juriste",
        nom: "<PERSON><PERSON> CHAARI",
        email: "<EMAIL>",
        motDePasse: await bcrypt.hash("juriste123", 12),
        role: "JURIDIQUE",
      },
      {
        username: "user",
        nom: "<PERSON><PERSON>",
        email: "<EMAIL>",
        motDePasse: await bcrypt.hash("user123", 12),
        role: "STANDARD",
      },
    ])
    console.log("✅ Users created")

    // Create proprietaires
    const proprietaires = await Proprietaire.bulkCreate([
      {
        nom: "Benali",
        prenom: "Ahmed",
        cin: "AB123456",
        telephone: "+212600000001",
        email: "<EMAIL>",
        adresse: "Rue Mohammed V, Casablanca",
        nationalite: "Marocaine",
        profession: "Ingénieur",
        createdBy: 1, // Will be the admin user
      },
      {
        nom: "Alaoui",
        prenom: "Fatima",
        cin: "FA789012",
        telephone: "+212600000002",
        email: "<EMAIL>",
        adresse: "Avenue Hassan II, Rabat",
        nationalite: "Marocaine",
        profession: "Médecin",
        createdBy: 1,
      },
      {
        nom: "Tazi",
        prenom: "Omar",
        cin: "OT345678",
        telephone: "+212600000003",
        email: "<EMAIL>",
        adresse: "Boulevard Zerktouni, Marrakech",
        nationalite: "Marocaine",
        profession: "Commerçant",
        createdBy: 1,
      },
    ])
    console.log("✅ Proprietaires created")

    // Create parcelles with real Moroccan coordinates
    const parcelles = await Parcelle.bulkCreate([
      {
        numeroTitreFoncier: "TF-123456",
        coordonnees: {
          type: "Polygon",
          coordinates: [
            [
              [-7.5898, 33.5731],
              [-7.5888, 33.5731],
              [-7.5888, 33.5741],
              [-7.5898, 33.5741],
              [-7.5898, 33.5731],
            ],
          ],
        },
        superficie: 2500.0,
        superficieUtile: 2200.0,
        adresse: "Kerkennah, Sfax",
        commune: "Sfax",
        province: "Sfax",
        region: "Sfax",
        zonage: "Résidentiel",
        valeurEstimee: 1500000.0,
        createdBy: 1,
      },
      {
        numeroTitreFoncier: "TF-789012",
        coordonnees: {
          type: "Polygon",
          coordinates: [
            [
              [-6.8416, 34.0209],
              [-6.8406, 34.0209],
              [-6.8406, 34.0219],
              [-6.8416, 34.0219],
              [-6.8416, 34.0209],
            ],
          ],
        },
        superficie: 1800.0,
        superficieUtile: 1600.0,
        adresse: "Chergui, Sfax",
        commune: "Sfax",
        province: "Sfax",
        region: "Sfax",
        zonage: "Commercial",
        valeurEstimee: 2200000.0,
        createdBy: 1,
      },
      {
        numeroTitreFoncier: "TF-345678",
        coordonnees: {
          type: "Polygon",
          coordinates: [
            [
              [-7.9811, 31.6295],
              [-7.9801, 31.6295],
              [-7.9801, 31.6305],
              [-7.9811, 31.6305],
              [-7.9811, 31.6295],
            ],
          ],
        },
        superficie: 3200.0,
        superficieUtile: 3000.0,
        adresse: "Kerkennah, Sfax",
        commune: "Sfax",
        province: "Sfax",
        region: "Sfax",
        zonage: "Touristique",
        valeurEstimee: 3500000.0,
        createdBy: 1,
      },
    ])
    console.log("✅ Parcelles created")

    // Create sample documents
    const documents = await Document.bulkCreate([
      {
        numero: "DOC-2024-001",
        nom: "Contrat de Location Résidentielle",
        type: "LOCATION",
        statut: "ACTIF",
        dateSignature: new Date("2024-01-15"),
        dateExpiration: new Date("2025-01-15"),
        clauses: "Contrat de location résidentielle avec option d'achat après 2 ans",
        montant: 5000.0,
        devise: "MAD",
        ParcelleId: parcelles[0].id,
        ProprietaireId: proprietaires[0].id,
        createdBy: users[0].id,
      },
      {
        numero: "DOC-2024-002",
        nom: "Acte de Vente Définitive",
        type: "ACHAT",
        statut: "ACTIF",
        dateSignature: new Date("2024-01-20"),
        dateExpiration: new Date("2024-12-31"),
        clauses: "Vente définitive avec garantie décennale et clause de non-concurrence",
        montant: 250000.0,
        devise: "MAD",
        ParcelleId: parcelles[1].id,
        ProprietaireId: proprietaires[1].id,
        createdBy: users[1].id,
      },
      {
        numero: "DOC-2024-003",
        nom: "Convention de Servitude",
        type: "SERVITUDE",
        statut: "EN_RENOUVELLEMENT",
        dateSignature: new Date("2023-06-10"),
        dateExpiration: new Date("2024-06-10"),
        clauses: "Droit de passage pour accès à la voie publique avec compensation financière",
        montant: 1200.0,
        devise: "MAD",
        ParcelleId: parcelles[2].id,
        ProprietaireId: proprietaires[2].id,
        createdBy: users[0].id,
      },
    ])
    console.log("✅ Documents created")

    console.log("🎉 Database seeding completed successfully!")
    console.log("\n📊 Summary:")
    console.log(`- ${users.length} users created`)
    console.log(`- ${proprietaires.length} proprietaires created`)
    console.log(`- ${parcelles.length} parcelles created`)
    console.log(`- ${documents.length} documents created`)

    console.log("\n🔐 Default login credentials:")
    console.log("Admin: <EMAIL> / admin123")
    console.log("Juriste: <EMAIL> / juriste123")
    console.log("User: <EMAIL> / user123")
  } catch (error) {
    console.error("❌ Error seeding database:", error)
    process.exit(1)
  } finally {
    await sequelize.close()
    process.exit(0)
  }
}

// Run seeding if this file is executed directly
if (require.main === module) {
  seedDatabase()
}

module.exports = seedDatabase
