const { body, validationResult } = require("express-validator")

const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: "Données invalides",
      details: errors.array(),
    })
  }
  next()
}

const validateLogin = [
  body("email").isEmail().normalizeEmail().withMessage("Email valide requis"),
  body("password").isLength({ min: 6 }).withMessage("Mot de passe requis (minimum 6 caractères)"),
  handleValidationErrors,
]

const validateRegister = [
  body("nom").trim().isLength({ min: 2, max: 100 }).withMessage("Nom requis (2-100 caractères)"),
  body("email").isEmail().normalizeEmail().withMessage("Email valide requis"),
  body("password")
    .isLength({ min: 6 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage("Mot de passe doit contenir au moins 6 caractères, une majuscule, une minuscule et un chiffre"),
  body("role").optional().isIn(["ADMIN", "JURIDIQUE", "STANDARD"]).withMessage("Rôle invalide"),
  handleValidationErrors,
]

const validateParcelle = [
  body("idParcelle").trim().notEmpty().withMessage("ID parcelle requis"),
  body("numeroTitreFoncier").trim().notEmpty().withMessage("Numéro de titre foncier requis"),
  body("superficie").isFloat({ min: 0 }).withMessage("Superficie doit être un nombre positif"),
  body("coordonnees").notEmpty().withMessage("Coordonnées requises"),
  handleValidationErrors,
]

const validateProprietaire = [
  body("nom").trim().isLength({ min: 2, max: 100 }).withMessage("Nom requis (2-100 caractères)"),
  body("prenom").trim().isLength({ min: 2, max: 100 }).withMessage("Prénom requis (2-100 caractères)"),
  body("cin").trim().isLength({ min: 6, max: 20 }).withMessage("CIN requis (6-20 caractères)"),
  body("email").optional().isEmail().normalizeEmail().withMessage("Email invalide"),
  body("telephone")
    .optional()
    .matches(/^[+]?[0-9\s\-$$$$]+$/)
    .withMessage("Numéro de téléphone invalide"),
  handleValidationErrors,
]

module.exports = {
  validateLogin,
  validateRegister,
  validateParcelle,
  validateProprietaire,
  handleValidationErrors,
}
