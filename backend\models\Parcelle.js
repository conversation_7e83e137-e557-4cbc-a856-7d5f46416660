const { Model, DataTypes } = require("sequelize")
const sequelize = require("../config/database")

class Pa<PERSON><PERSON> extends Model {}

Parcelle.init(
  {
    idParcelle: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    numeroTitreFoncier: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
    adresse: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    superficie: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },
    typeTerrain: {
      type: DataTypes.ENUM("agricole", "urbain", "forestier", "autre"),
      defaultValue: "urbain",
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    coordonneesGPS: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    dateAcquisition: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    statut: {
      type: DataTypes.ENUM("libre", "occupe", "en_construction", "en_litige"),
      defaultValue: "libre",
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "users",
        key: "idUser",
      },
    },
  },
  {
    sequelize,
    modelName: "Parcelle",
    tableName: "parcelles",
    timestamps: true,
  }
)

module.exports = Parcelle 