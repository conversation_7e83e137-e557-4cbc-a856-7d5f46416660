const { Model, DataTypes } = require("sequelize")
const sequelize = require("../config/database")

class Document extends Model {}

Document.init(
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    type: {
      type: DataTypes.ENUM(
        "titre_foncier",
        "acte_vente",
        "bail",
        "permis_construction",
        "autre"
      ),
      allowNull: false,
    },
    numero: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    dateEmission: {
      type: DataTypes.DATEONLY,
      allowNull: false,
    },
    dateExpiration: {
      type: DataTypes.DATEONLY,
      allowNull: true,
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    fichier: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    statut: {
      type: DataTypes.ENUM("valide", "expire", "en_cours", "annule"),
      defaultValue: "valide",
    },
    ProprietaireId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "proprietaires",
        key: "id",
      },
    },
    ParcelleId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "parcelles",
        key: "id",
      },
    },
    createdBy: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: "users",
        key: "id",
      },
    },
  },
  {
    sequelize,
    modelName: "Document",
    tableName: "documents",
    timestamps: true,
  }
)

module.exports = Document 